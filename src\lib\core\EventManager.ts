// PhotoEditor 2.0 - 事件管理器
// 负责编辑器内部事件的发布和订阅

import type { EditorEvent, EventHandler } from '$lib/types';

/**
 * 事件管理器
 * 提供发布-订阅模式的事件系统
 */
export class EventManager {
  private listeners: Map<string, EventHandler[]> = new Map();
  private eventHistory: EditorEvent[] = [];
  private maxHistorySize = 1000;

  /**
   * 订阅事件
   */
  on(eventType: string, handler: EventHandler): void {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, []);
    }
    this.listeners.get(eventType)!.push(handler);
  }

  /**
   * 取消订阅事件
   */
  off(eventType: string, handler: EventHandler): void {
    const handlers = this.listeners.get(eventType);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  /**
   * 发布事件
   */
  emit(eventType: string, data?: any): void {
    const event: EditorEvent = {
      type: eventType,
      timestamp: Date.now(),
      data
    };

    // 记录事件历史
    this.eventHistory.push(event);
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory.shift();
    }

    // 通知所有监听器
    const handlers = this.listeners.get(eventType);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(event);
        } catch (error) {
          console.error(`Error in event handler for ${eventType}:`, error);
        }
      });
    }
  }

  /**
   * 一次性事件监听
   */
  once(eventType: string, handler: EventHandler): void {
    const onceHandler: EventHandler = event => {
      handler(event);
      this.off(eventType, onceHandler);
    };
    this.on(eventType, onceHandler);
  }

  /**
   * 获取事件历史
   */
  getEventHistory(): EditorEvent[] {
    return [...this.eventHistory];
  }

  /**
   * 清除事件历史
   */
  clearEventHistory(): void {
    this.eventHistory = [];
  }

  /**
   * 销毁事件管理器
   */
  destroy(): void {
    this.listeners.clear();
    this.eventHistory = [];
  }
}
