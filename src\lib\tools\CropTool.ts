// PhotoEditor 2.0 - 裁剪工具
// 负责图像裁剪功能

import { BaseTool } from '$lib/tools/BaseTool';
import { ToolType, type CropArea } from '$lib/types';
import { fabric } from 'fabric';

/**
 * 裁剪工具
 * 用于裁剪图像
 */
export class CropTool extends BaseTool {
  type: ToolType = ToolType.CROP;
  name = '裁剪工具';
  icon = 'crop';
  cursor = 'crosshair';

  private cropRect: fabric.Rect | null = null;
  private isDrawing = false;
  private startPoint: fabric.Point | null = null;

  protected onActivate(): void {
    // 禁用对象选择
    this.canvas.selection = false;
    this.canvas.forEachObject(obj => {
      obj.selectable = false;
      obj.evented = false;
    });

    this.eventManager.emit('crop:activated');
  }

  protected onDeactivate(): void {
    // 清除裁剪框
    this.clearCropRect();

    // 恢复对象选择
    this.canvas.selection = true;
    this.canvas.forEachObject(obj => {
      obj.selectable = true;
      obj.evented = true;
    });

    this.eventManager.emit('crop:deactivated');
  }

  protected onOptionsUpdate(): void {
    // 裁剪工具选项更新
  }

  onMouseDown(event: fabric.IEvent): void {
    const pointer = this.canvas.getPointer(event.e);
    this.startPoint = new fabric.Point(pointer.x, pointer.y);
    this.isDrawing = true;

    // 清除之前的裁剪框
    this.clearCropRect();

    // 创建新的裁剪框
    this.cropRect = new fabric.Rect({
      left: pointer.x,
      top: pointer.y,
      width: 0,
      height: 0,
      fill: 'transparent',
      stroke: '#007acc',
      strokeWidth: 2,
      strokeDashArray: [5, 5],
      selectable: false,
      evented: false
    });

    this.canvas.add(this.cropRect);
    this.canvas.renderAll();
  }

  onMouseMove(event: fabric.IEvent): void {
    if (!this.isDrawing || !this.cropRect || !this.startPoint) return;

    const pointer = this.canvas.getPointer(event.e);

    // 计算裁剪框尺寸
    const width = Math.abs(pointer.x - this.startPoint.x);
    const height = Math.abs(pointer.y - this.startPoint.y);

    // 更新裁剪框位置和尺寸
    this.cropRect.set({
      left: Math.min(pointer.x, this.startPoint.x),
      top: Math.min(pointer.y, this.startPoint.y),
      width: width,
      height: height
    });

    this.canvas.renderAll();
  }

  onMouseUp(event: fabric.IEvent): void {
    if (!this.isDrawing || !this.cropRect) return;

    this.isDrawing = false;

    // 检查裁剪框是否有效（有一定尺寸）
    if (this.cropRect.width! > 10 && this.cropRect.height! > 10) {
      const cropArea: CropArea = {
        x: this.cropRect.left!,
        y: this.cropRect.top!,
        width: this.cropRect.width!,
        height: this.cropRect.height!
      };

      this.eventManager.emit('crop:area-selected', { cropArea });
    } else {
      // 裁剪框太小，清除它
      this.clearCropRect();
    }
  }

  onKeyDown(event: KeyboardEvent): void {
    switch (event.key) {
      case 'Enter':
        this.applyCrop();
        event.preventDefault();
        break;

      case 'Escape':
        this.clearCropRect();
        event.preventDefault();
        break;
    }
  }

  /**
   * 应用裁剪
   */
  private applyCrop(): void {
    if (!this.cropRect) return;

    const cropArea: CropArea = {
      x: this.cropRect.left!,
      y: this.cropRect.top!,
      width: this.cropRect.width!,
      height: this.cropRect.height!
    };

    // 执行裁剪操作
    this.performCrop(cropArea);

    // 清除裁剪框
    this.clearCropRect();

    this.eventManager.emit('crop:applied', { cropArea });
  }

  /**
   * 执行裁剪操作
   */
  private performCrop(cropArea: CropArea): void {
    try {
      // 获取画布数据
      const canvasData = this.canvas.toDataURL({
        format: 'png',
        left: cropArea.x,
        top: cropArea.y,
        width: cropArea.width,
        height: cropArea.height
      });

      // 创建新图像
      fabric.Image.fromURL(canvasData, img => {
        if (!img) {
          console.error('Failed to create image from canvas data');
          return;
        }

        // 清除画布
        this.canvas.clear();

        // 调整画布尺寸
        this.canvas.setDimensions({
          width: cropArea.width,
          height: cropArea.height
        });

        // 添加裁剪后的图像
        img.set({
          left: 0,
          top: 0,
          selectable: true,
          evented: true
        });

        this.canvas.add(img);
        this.canvas.renderAll();
      });
    } catch (error) {
      console.error('Error performing crop:', error);
      this.eventManager.emit('crop:error', { error });
    }
  }

  /**
   * 清除裁剪框
   */
  private clearCropRect(): void {
    if (this.cropRect) {
      this.canvas.remove(this.cropRect);
      this.cropRect = null;
      this.canvas.renderAll();
    }
  }

  /**
   * 获取当前裁剪区域
   */
  getCropArea(): CropArea | null {
    if (!this.cropRect) return null;

    return {
      x: this.cropRect.left!,
      y: this.cropRect.top!,
      width: this.cropRect.width!,
      height: this.cropRect.height!
    };
  }
}
