// PhotoEditor 2.0 - 核心类型定义
// 基于项目技术规格文档的类型系统

import type { fabric } from 'fabric';

// ============================================================================
// 基础类型定义
// ============================================================================

export interface Point {
  x: number;
  y: number;
}

export interface Size {
  width: number;
  height: number;
}

export interface Rectangle extends Point, Size {}

export interface Color {
  r: number;
  g: number;
  b: number;
  a?: number;
}

// ============================================================================
// 编辑器核心类型
// ============================================================================

export interface EditorOptions {
  container: HTMLElement;
  width?: number;
  height?: number;
  backgroundColor?: string;
  enableTouch?: boolean;
  enableKeyboard?: boolean;
  maxHistorySteps?: number;
}

export interface EditorState {
  canvas: fabric.Canvas | null;
  activeTool: ToolType;
  zoom: number;
  pan: Point;
  isLoading: boolean;
  error: string | null;
  isDirty: boolean;
}

// ============================================================================
// 工具系统类型
// ============================================================================

export enum ToolType {
  SELECTION = 'selection',
  MOVE = 'move',
  CROP = 'crop',
  BRUSH = 'brush',
  ERASER = 'eraser',
  TEXT = 'text',
  SHAPE = 'shape',
  MOSAIC = 'mosaic',
  BLUR = 'blur',
  ZOOM = 'zoom',
  PAN = 'pan'
}

export interface ToolOptions {
  size?: number;
  opacity?: number;
  color?: Color;
  hardness?: number;
  [key: string]: any;
}

export interface Tool {
  type: ToolType;
  name: string;
  icon: string;
  cursor: string;
  options: ToolOptions;
  isActive: boolean;
}

// ============================================================================
// 图层系统类型
// ============================================================================

export enum LayerType {
  IMAGE = 'image',
  TEXT = 'text',
  SHAPE = 'shape',
  ADJUSTMENT = 'adjustment',
  GROUP = 'group'
}

export enum BlendMode {
  NORMAL = 'normal',
  MULTIPLY = 'multiply',
  SCREEN = 'screen',
  OVERLAY = 'overlay',
  SOFT_LIGHT = 'soft-light',
  HARD_LIGHT = 'hard-light',
  COLOR_DODGE = 'color-dodge',
  COLOR_BURN = 'color-burn',
  DARKEN = 'darken',
  LIGHTEN = 'lighten',
  DIFFERENCE = 'difference',
  EXCLUSION = 'exclusion'
}

export interface Layer {
  id: string;
  name: string;
  type: LayerType;
  visible: boolean;
  opacity: number;
  blendMode: BlendMode;
  locked: boolean;
  bounds: Rectangle;
  data: LayerData;
  parentId?: string;
  children?: string[];
  createdAt: number;
  updatedAt: number;
}

export interface LayerData {
  [key: string]: any;
}

// ============================================================================
// 选区系统类型
// ============================================================================

export enum SelectionType {
  RECTANGLE = 'rectangle',
  ELLIPSE = 'ellipse',
  LASSO = 'lasso',
  POLYGON = 'polygon',
  MAGIC_WAND = 'magic-wand',
  QUICK_SELECT = 'quick-select'
}

export interface Selection {
  id: string;
  type: SelectionType;
  bounds: Rectangle;
  path?: Point[];
  pixels?: Set<string>;
  feather: number;
  antiAlias: boolean;
}

// ============================================================================
// 滤镜系统类型
// ============================================================================

export enum FilterType {
  BLUR = 'blur',
  GAUSSIAN_BLUR = 'gaussian-blur',
  MOTION_BLUR = 'motion-blur',
  SHARPEN = 'sharpen',
  UNSHARP_MASK = 'unsharp-mask',
  BRIGHTNESS = 'brightness',
  CONTRAST = 'contrast',
  SATURATION = 'saturation',
  HUE = 'hue',
  LEVELS = 'levels',
  CURVES = 'curves',
  COLOR_BALANCE = 'color-balance',
  NOISE = 'noise',
  EMBOSS = 'emboss',
  EDGE_DETECT = 'edge-detect'
}

export interface FilterParams {
  [key: string]: number | boolean | string | Color;
}

export interface Filter {
  type: FilterType;
  name: string;
  params: FilterParams;
  enabled: boolean;
}

// ============================================================================
// 图像变换类型
// ============================================================================

export interface CropArea extends Rectangle {}

export enum ResizeAlgorithm {
  NEAREST = 'nearest',
  BILINEAR = 'bilinear',
  BICUBIC = 'bicubic',
  LANCZOS = 'lanczos'
}

export interface TransformOptions {
  x?: number;
  y?: number;
  scaleX?: number;
  scaleY?: number;
  rotation?: number;
  skewX?: number;
  skewY?: number;
}

// ============================================================================
// 打码功能类型
// ============================================================================

export enum MosaicEffect {
  BLUR = 'blur',
  MOSAIC = 'mosaic',
  PIXELATE = 'pixelate'
}

export interface MosaicOptions {
  effect: MosaicEffect;
  intensity: number;
  size?: number;
  feather?: number;
}

// ============================================================================
// 文件系统类型
// ============================================================================

export enum ImageFormat {
  JPEG = 'image/jpeg',
  PNG = 'image/png',
  WEBP = 'image/webp',
  GIF = 'image/gif',
  SVG = 'image/svg+xml'
}

export interface ExportOptions {
  format: ImageFormat;
  quality?: number;
  width?: number;
  height?: number;
  backgroundColor?: Color;
}

export interface ProjectData {
  id: string;
  name: string;
  version: string;
  canvas: {
    width: number;
    height: number;
    backgroundColor: Color;
  };
  layers: Layer[];
  history: HistoryEntry[];
  metadata: ProjectMetadata;
  createdAt: number;
  updatedAt: number;
}

export interface ProjectMetadata {
  author?: string;
  description?: string;
  tags?: string[];
  [key: string]: any;
}

// ============================================================================
// 历史系统类型
// ============================================================================

export interface HistoryEntry {
  id: string;
  name: string;
  timestamp: number;
  data: any;
  preview?: string;
}

export interface HistoryState {
  past: HistoryEntry[];
  present: HistoryEntry | null;
  future: HistoryEntry[];
  canUndo: boolean;
  canRedo: boolean;
  maxSteps: number;
}

// ============================================================================
// AI 功能类型
// ============================================================================

export interface AIModel {
  name: string;
  version: string;
  url: string;
  size: number;
  loaded: boolean;
}

export interface AIProcessingOptions {
  model?: string;
  confidence?: number;
  batchSize?: number;
  useGPU?: boolean;
}

// ============================================================================
// 事件系统类型
// ============================================================================

export interface EditorEvent {
  type: string;
  timestamp: number;
  data?: any;
}

export interface EventHandler {
  (event: EditorEvent): void;
}

// ============================================================================
// 性能监控类型
// ============================================================================

export interface PerformanceMetrics {
  renderTime: number[];
  memoryUsage: number[];
  userInteractions: UserInteraction[];
}

export interface UserInteraction {
  type: string;
  timestamp: number;
  duration: number;
  target?: string;
}

// ============================================================================
// 设备信息类型
// ============================================================================

export interface DeviceInfo {
  isLowEnd: boolean;
  hasWebGL: boolean;
  maxTextureSize: number;
  memory: number;
  connection: string;
  isMobile: boolean;
  isTouch: boolean;
}

// ============================================================================
// 错误处理类型
// ============================================================================

export interface EditorError extends Error {
  code: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  context?: any;
  timestamp: number;
}

// ============================================================================
// 插件系统类型（为未来扩展预留）
// ============================================================================

export interface Plugin {
  name: string;
  version: string;
  description: string;
  author: string;
  main: string;
  dependencies?: string[];
  enabled: boolean;
}

export interface PluginAPI {
  registerTool: (tool: Tool) => void;
  registerFilter: (filter: Filter) => void;
  registerCommand: (name: string, handler: Function) => void;
  getEditor: () => any;
}
