# PhotoEditor 2.0 环境变量配置示例
# 复制此文件为 .env 并填入实际值

# 应用配置
PUBLIC_APP_NAME="PhotoEditor 2.0"
PUBLIC_APP_VERSION="0.1.0"
PUBLIC_APP_DESCRIPTION="专业的在线图片编辑器"

# API 配置
PUBLIC_API_BASE_URL="https://api.photoeditor.com"
PUBLIC_API_VERSION="v1"

# AI 服务配置
PUBLIC_AI_SERVICE_URL="https://ai.photoeditor.com"
AI_SERVICE_API_KEY="your-ai-service-api-key"

# 文件上传配置
PUBLIC_MAX_FILE_SIZE="10485760" # 10MB
PUBLIC_ALLOWED_FILE_TYPES="image/jpeg,image/png,image/webp,image/gif"

# 存储配置
STORAGE_PROVIDER="local" # local, s3, cloudinary
S3_BUCKET_NAME="photoeditor-uploads"
S3_REGION="us-east-1"
S3_ACCESS_KEY_ID="your-s3-access-key"
S3_SECRET_ACCESS_KEY="your-s3-secret-key"

CLOUDINARY_CLOUD_NAME="your-cloudinary-cloud-name"
CLOUDINARY_API_KEY="your-cloudinary-api-key"
CLOUDINARY_API_SECRET="your-cloudinary-api-secret"

# 数据库配置（如果需要）
DATABASE_URL="postgresql://user:password@localhost:5432/photoeditor"

# Redis 配置（用于缓存和会话）
REDIS_URL="redis://localhost:6379"

# 认证配置
JWT_SECRET="your-jwt-secret-key"
SESSION_SECRET="your-session-secret-key"

# OAuth 配置
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"

# 邮件服务配置
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-email-password"

# 监控和分析
SENTRY_DSN="your-sentry-dsn"
GOOGLE_ANALYTICS_ID="your-ga-id"

# 开发配置
NODE_ENV="development"
DEBUG="true"
LOG_LEVEL="debug"

# 生产配置
PRODUCTION_URL="https://photoeditor.com"
CDN_URL="https://cdn.photoeditor.com"

# 功能开关
FEATURE_AI_BACKGROUND_REMOVAL="true"
FEATURE_FACE_DETECTION="true"
FEATURE_BATCH_PROCESSING="false"
FEATURE_COLLABORATION="false"

# 性能配置
ENABLE_COMPRESSION="true"
ENABLE_CACHING="true"
CACHE_TTL="3600" # 1 hour

# 安全配置
ENABLE_RATE_LIMITING="true"
RATE_LIMIT_REQUESTS="100"
RATE_LIMIT_WINDOW="900000" # 15 minutes

ENABLE_CSRF_PROTECTION="true"
ENABLE_HELMET="true"

# 第三方服务
UNSPLASH_ACCESS_KEY="your-unsplash-access-key"
PEXELS_API_KEY="your-pexels-api-key"

# WebRTC 配置（用于实时协作）
WEBRTC_STUN_SERVER="stun:stun.l.google.com:19302"
WEBRTC_TURN_SERVER="turn:your-turn-server.com"
WEBRTC_TURN_USERNAME="your-turn-username"
WEBRTC_TURN_PASSWORD="your-turn-password"

# 支付配置（如果需要付费功能）
STRIPE_PUBLIC_KEY="your-stripe-public-key"
STRIPE_SECRET_KEY="your-stripe-secret-key"
STRIPE_WEBHOOK_SECRET="your-stripe-webhook-secret"

# 部署配置
VERCEL_TOKEN="your-vercel-token"
NETLIFY_AUTH_TOKEN="your-netlify-token"

# 测试配置
TEST_DATABASE_URL="postgresql://user:password@localhost:5432/photoeditor_test"
TEST_REDIS_URL="redis://localhost:6379/1"
