// PhotoEditor 2.0 - 工具管理器
// 负责管理编辑器的各种工具

import type { EventManager } from '$lib/core/EventManager';
import { ToolType, type Tool, type ToolOptions } from '$lib/types';
import { fabric } from 'fabric';

// 导入基础工具类
import { BaseTool } from '$lib/tools/BaseTool';

// 导入具体工具类
import { BrushTool } from '$lib/tools/BrushTool';
import { CropTool } from '$lib/tools/CropTool';
import { MosaicTool } from '$lib/tools/MosaicTool';
import { SelectionTool } from '$lib/tools/SelectionTool';

/**
 * 工具管理器
 * 负责注册、管理和切换各种编辑工具
 */
export class ToolManager {
  private canvas: fabric.Canvas;
  private eventManager: EventManager;
  private tools: Map<ToolType, BaseTool> = new Map();
  private activeTool: BaseTool | null = null;
  private defaultOptions: ToolOptions = {
    size: 10,
    opacity: 1,
    color: { r: 0, g: 0, b: 0, a: 1 },
    hardness: 1
  };

  constructor(canvas: fabric.Canvas, eventManager: EventManager) {
    this.canvas = canvas;
    this.eventManager = eventManager;

    this.initializeTools();
    this.bindCanvasEvents();
  }

  /**
   * 初始化所有工具
   */
  private initializeTools(): void {
    // 注册默认工具
    this.registerTool(new SelectionTool(this.canvas, this.eventManager, this.defaultOptions));
    this.registerTool(new BrushTool(this.canvas, this.eventManager, this.defaultOptions));
    this.registerTool(new CropTool(this.canvas, this.eventManager, this.defaultOptions));
    this.registerTool(new MosaicTool(this.canvas, this.eventManager, this.defaultOptions));

    // 默认选择选择工具
    this.selectTool(ToolType.SELECTION);
  }

  /**
   * 注册工具
   */
  registerTool(tool: BaseTool): void {
    this.tools.set(tool.type, tool);
    this.eventManager.emit('tool:registered', { tool: tool.type });
  }

  /**
   * 注销工具
   */
  unregisterTool(toolType: ToolType): void {
    const tool = this.tools.get(toolType);
    if (tool) {
      if (this.activeTool === tool) {
        this.selectTool(ToolType.SELECTION); // 切换到默认工具
      }
      tool.deactivate();
      this.tools.delete(toolType);
      this.eventManager.emit('tool:unregistered', { tool: toolType });
    }
  }

  /**
   * 选择工具
   */
  selectTool(toolType: ToolType): void {
    const tool = this.tools.get(toolType);
    if (!tool) {
      console.warn(`Tool ${toolType} not found`);
      return;
    }

    // 停用当前工具
    if (this.activeTool) {
      this.activeTool.deactivate();
    }

    // 激活新工具
    this.activeTool = tool;
    this.activeTool.activate();

    // 更新画布光标
    this.canvas.defaultCursor = tool.cursor;
    this.canvas.hoverCursor = tool.cursor;
    this.canvas.moveCursor = tool.cursor;

    this.eventManager.emit('tool:selected', { tool: toolType });
  }

  /**
   * 获取当前激活的工具
   */
  getActiveTool(): BaseTool | null {
    return this.activeTool;
  }

  /**
   * 获取所有已注册的工具
   */
  getAllTools(): Tool[] {
    return Array.from(this.tools.values()).map(tool => ({
      type: tool.type,
      name: tool.name,
      icon: tool.icon,
      cursor: tool.cursor,
      options: tool.getOptions(),
      isActive: tool === this.activeTool
    }));
  }

  /**
   * 更新工具选项
   */
  updateToolOptions(toolType: ToolType, options: Partial<ToolOptions>): void {
    const tool = this.tools.get(toolType);
    if (tool) {
      tool.updateOptions(options);
      this.eventManager.emit('tool:options-updated', { tool: toolType, options });
    }
  }

  /**
   * 绑定画布事件
   */
  private bindCanvasEvents(): void {
    // 鼠标事件
    this.canvas.on('mouse:down', this.onMouseDown.bind(this));
    this.canvas.on('mouse:move', this.onMouseMove.bind(this));
    this.canvas.on('mouse:up', this.onMouseUp.bind(this));
    this.canvas.on('mouse:wheel', this.onMouseWheel.bind(this));

    // 键盘事件
    document.addEventListener('keydown', this.onKeyDown.bind(this));
    document.addEventListener('keyup', this.onKeyUp.bind(this));

    // 触摸事件
    this.canvas.upperCanvasEl.addEventListener('touchstart', this.onTouchStart.bind(this));
    this.canvas.upperCanvasEl.addEventListener('touchmove', this.onTouchMove.bind(this));
    this.canvas.upperCanvasEl.addEventListener('touchend', this.onTouchEnd.bind(this));
  }

  // 事件处理方法
  private onMouseDown(event: fabric.IEvent): void {
    if (this.activeTool?.onMouseDown) {
      this.activeTool.onMouseDown(event);
    }
  }

  private onMouseMove(event: fabric.IEvent): void {
    if (this.activeTool?.onMouseMove) {
      this.activeTool.onMouseMove(event);
    }
  }

  private onMouseUp(event: fabric.IEvent): void {
    if (this.activeTool?.onMouseUp) {
      this.activeTool.onMouseUp(event);
    }
  }

  private onMouseWheel(event: fabric.IEvent): void {
    if (this.activeTool?.onMouseWheel) {
      this.activeTool.onMouseWheel(event);
    }
  }

  private onKeyDown(event: KeyboardEvent): void {
    if (this.activeTool?.onKeyDown) {
      this.activeTool.onKeyDown(event);
    }
  }

  private onKeyUp(event: KeyboardEvent): void {
    if (this.activeTool?.onKeyUp) {
      this.activeTool.onKeyUp(event);
    }
  }

  private onTouchStart(event: TouchEvent): void {
    if (this.activeTool?.onTouchStart) {
      this.activeTool.onTouchStart(event);
    }
  }

  private onTouchMove(event: TouchEvent): void {
    if (this.activeTool?.onTouchMove) {
      this.activeTool.onTouchMove(event);
    }
  }

  private onTouchEnd(event: TouchEvent): void {
    if (this.activeTool?.onTouchEnd) {
      this.activeTool.onTouchEnd(event);
    }
  }

  /**
   * 销毁工具管理器
   */
  destroy(): void {
    // 停用当前工具
    if (this.activeTool) {
      this.activeTool.deactivate();
    }

    // 清理所有工具
    this.tools.clear();

    // 移除事件监听器
    document.removeEventListener('keydown', this.onKeyDown.bind(this));
    document.removeEventListener('keyup', this.onKeyUp.bind(this));
  }
}
