// PhotoEditor 2.0 - 基础 E2E 测试
// 测试编辑器的基本功能和用户交互

import { expect, test } from '@playwright/test';

// Playwright 代码覆盖率类型定义
interface JSCoverageRange {
  count: number;
  startOffset: number;
  endOffset: number;
}

interface JSCoverageFunction {
  functionName: string;
  isBlockCoverage: boolean;
  ranges: JSCoverageRange[];
}

interface JSCoverageEntry {
  url: string;
  scriptId: string;
  source?: string;
  functions: JSCoverageFunction[];
}

test.describe('PhotoEditor 2.0 基础功能', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('页面加载和基本元素显示', async ({ page }) => {
    // 检查页面标题
    await expect(page).toHaveTitle(/PhotoEditor 2.0/);

    // 检查主要UI元素
    await expect(page.locator('h1')).toContainText('PhotoEditor 2.0');
    await expect(page.locator('text=打开图片')).toBeVisible();
    await expect(page.locator('text=保存项目')).toBeVisible();

    // 检查工具栏
    await expect(page.locator('[title="选择工具"]')).toBeVisible();
    await expect(page.locator('[title="画笔工具"]')).toBeVisible();
    await expect(page.locator('[title="裁剪工具"]')).toBeVisible();
    await expect(page.locator('[title="打码工具"]')).toBeVisible();

    // 检查属性面板
    await expect(page.locator('text=属性面板')).toBeVisible();
    await expect(page.locator('text=图层')).toBeVisible();
    await expect(page.locator('text=工具选项')).toBeVisible();
    await expect(page.locator('text=滤镜效果')).toBeVisible();
  });

  test('编辑器初始化', async ({ page }) => {
    // 等待编辑器加载完成
    await page.waitForSelector('.canvas-container', { timeout: 10000 });

    // 检查是否显示拖拽提示
    await expect(page.locator('text=拖拽图片到此处开始编辑')).toBeVisible();

    // 检查编辑器容器是否存在
    const editorContainer = page.locator('[bind\\:this]');
    await expect(editorContainer).toBeVisible();
  });

  test('工具选择功能', async ({ page }) => {
    // 等待页面加载
    await page.waitForLoadState('networkidle');

    // 测试选择不同工具
    const tools = [
      { selector: '[title="选择工具"]', name: '选择工具' },
      { selector: '[title="画笔工具"]', name: '画笔工具' },
      { selector: '[title="裁剪工具"]', name: '裁剪工具' },
      { selector: '[title="打码工具"]', name: '打码工具' }
    ];

    for (const tool of tools) {
      await page.click(tool.selector);
      // 验证工具被选中（可以通过样式变化或其他视觉反馈）
      await expect(page.locator(tool.selector)).toBeVisible();
    }
  });

  test('撤销重做按钮状态', async ({ page }) => {
    // 检查撤销重做按钮初始状态
    const undoButton = page.locator('[title="撤销 (Ctrl+Z)"]');
    const redoButton = page.locator('[title="重做 (Ctrl+Shift+Z)"]');

    await expect(undoButton).toBeVisible();
    await expect(redoButton).toBeVisible();

    // 初始状态下按钮应该是禁用的
    await expect(undoButton).toBeDisabled();
    await expect(redoButton).toBeDisabled();
  });

  test('响应式设计', async ({ page }) => {
    // 测试桌面视图
    await page.setViewportSize({ width: 1920, height: 1080 });
    await expect(page.locator('.editor-sidebar')).toBeVisible();

    // 测试平板视图
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.locator('.editor-main')).toBeVisible();

    // 测试移动视图
    await page.setViewportSize({ width: 375, height: 667 });
    await expect(page.locator('h1')).toBeVisible();
  });

  test('键盘快捷键', async ({ page }) => {
    // 等待页面加载
    await page.waitForLoadState('networkidle');

    // 测试撤销快捷键 (Ctrl+Z)
    await page.keyboard.press('Control+z');

    // 测试重做快捷键 (Ctrl+Shift+Z)
    await page.keyboard.press('Control+Shift+z');

    // 测试保存快捷键 (Ctrl+S)
    await page.keyboard.press('Control+s');

    // 由于没有实际图片，这些操作不会有明显效果
    // 但至少验证快捷键不会导致页面错误
  });

  test('错误处理', async ({ page }) => {
    // 监听控制台错误
    const errors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });

    // 监听页面错误
    page.on('pageerror', error => {
      errors.push(error.message);
    });

    // 执行一些可能导致错误的操作
    await page.click('[title="画笔工具"]');
    await page.click('[title="裁剪工具"]');

    // 等待一段时间确保所有异步操作完成
    await page.waitForTimeout(2000);

    // 检查是否有严重错误
    const criticalErrors = errors.filter(
      error => !error.includes('Warning') && !error.includes('favicon') && !error.includes('404')
    );

    expect(criticalErrors).toHaveLength(0);
  });

  test('性能基准', async ({ page }) => {
    // 开始性能监控
    await page.coverage.startJSCoverage();

    const startTime = Date.now();

    // 执行基本操作
    await page.click('[title="选择工具"]');
    await page.click('[title="画笔工具"]');
    await page.click('[title="裁剪工具"]');

    const endTime = Date.now();
    const duration = endTime - startTime;

    // 停止性能监控
    const coverage = await page.coverage.stopJSCoverage();

    // 验证性能指标
    expect(duration).toBeLessThan(5000); // 操作应在5秒内完成

    // 验证代码覆盖率
    // 计算源代码总字节数（使用 source 属性而不是 text）
    const totalBytes = coverage.reduce((total: number, entry: JSCoverageEntry) => {
      // 如果 source 不存在，则使用 0
      return total + (entry.source?.length || 0);
    }, 0);

    // 计算已执行的代码字节数
    const usedBytes = coverage.reduce((total: number, entry: JSCoverageEntry) => {
      // 对每个文件，计算所有函数的已执行代码
      const entryUsed = entry.functions.reduce(
        (functionTotal: number, func: JSCoverageFunction) => {
          // 对每个函数，计算所有已执行范围的字节数
          const functionUsed = func.ranges.reduce((sum: number, range: JSCoverageRange) => {
            // 使用 startOffset 和 endOffset 而不是 start 和 end
            return sum + (range.endOffset - range.startOffset);
          }, 0);
          return functionTotal + functionUsed;
        },
        0
      );
      return total + entryUsed;
    }, 0);

    const coveragePercentage = totalBytes > 0 ? (usedBytes / totalBytes) * 100 : 0;
    console.log(`代码覆盖率: ${coveragePercentage.toFixed(2)}%`);
  });
});
