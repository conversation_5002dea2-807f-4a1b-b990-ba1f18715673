// PhotoEditor 2.0 - 基础工具抽象类
// 所有编辑工具的基类

import { fabric } from 'fabric';
import type { ToolOptions, ToolType } from '$lib/types';
import type { EventManager } from '$lib/core/EventManager';

/**
 * 基础工具抽象类
 * 所有编辑工具都应该继承此类
 */
export abstract class BaseTool {
  abstract type: ToolType;
  abstract name: string;
  abstract icon: string;
  abstract cursor: string;

  protected canvas: fabric.Canvas;
  protected eventManager: EventManager;
  protected options: ToolOptions;
  protected isActive = false;

  constructor(canvas: fabric.Canvas, eventManager: EventManager, options: ToolOptions = {}) {
    this.canvas = canvas;
    this.eventManager = eventManager;
    this.options = options;
  }

  /**
   * 激活工具
   */
  activate(): void {
    this.isActive = true;
    this.onActivate();
    this.eventManager.emit('tool:activated', { tool: this.type });
  }

  /**
   * 停用工具
   */
  deactivate(): void {
    this.isActive = false;
    this.onDeactivate();
    this.eventManager.emit('tool:deactivated', { tool: this.type });
  }

  /**
   * 更新工具选项
   */
  updateOptions(options: Partial<ToolOptions>): void {
    this.options = { ...this.options, ...options };
    this.onOptionsUpdate();
  }

  /**
   * 获取工具选项
   */
  getOptions(): ToolOptions {
    return { ...this.options };
  }

  // 抽象方法，由具体工具实现
  protected abstract onActivate(): void;
  protected abstract onDeactivate(): void;
  protected abstract onOptionsUpdate(): void;

  // 鼠标事件处理（可选实现）
  onMouseDown?(event: fabric.IEvent): void;
  onMouseMove?(event: fabric.IEvent): void;
  onMouseUp?(event: fabric.IEvent): void;
  onMouseWheel?(event: fabric.IEvent): void;

  // 键盘事件处理（可选实现）
  onKeyDown?(event: KeyboardEvent): void;
  onKeyUp?(event: KeyboardEvent): void;

  // 触摸事件处理（可选实现）
  onTouchStart?(event: TouchEvent): void;
  onTouchMove?(event: TouchEvent): void;
  onTouchEnd?(event: TouchEvent): void;
}
