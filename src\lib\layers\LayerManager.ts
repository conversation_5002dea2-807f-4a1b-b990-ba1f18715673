// PhotoEditor 2.0 - 图层管理器
// 负责管理编辑器的图层系统

import type { EventManager } from '$lib/core/EventManager';
import { BlendMode, LayerType, type Layer, type LayerData, type Rectangle } from '$lib/types';
import { fabric } from 'fabric';

/**
 * 将 Fabric.js 的 getBoundingRect 结果转换为 Rectangle 类型
 */
function fabricBoundsToRectangle(bounds: {
  left: number;
  top: number;
  width: number;
  height: number;
}): Rectangle {
  return {
    x: bounds.left,
    y: bounds.top,
    width: bounds.width,
    height: bounds.height
  };
}

/**
 * 图层管理器
 * 负责图层的创建、删除、排序和属性管理
 */
export class LayerManager {
  private canvas: fabric.Canvas;
  private eventManager: EventManager;
  private layers: Map<string, Layer> = new Map();
  private layerOrder: string[] = [];
  private activeLayerId: string | null = null;

  constructor(canvas: fabric.Canvas, eventManager: EventManager) {
    this.canvas = canvas;
    this.eventManager = eventManager;

    this.bindCanvasEvents();
  }

  /**
   * 绑定画布事件
   */
  private bindCanvasEvents(): void {
    this.canvas.on('object:added', this.onObjectAdded.bind(this));
    this.canvas.on('object:removed', this.onObjectRemoved.bind(this));
    this.canvas.on('object:modified', this.onObjectModified.bind(this));
  }

  /**
   * 添加图层
   */
  addLayer(type: LayerType, data?: LayerData): Layer {
    const layer: Layer = {
      id: this.generateLayerId(),
      name: this.generateLayerName(type),
      type,
      visible: true,
      opacity: 1,
      blendMode: BlendMode.NORMAL,
      locked: false,
      bounds: { x: 0, y: 0, width: 0, height: 0 },
      data: data || {},
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    this.layers.set(layer.id, layer);
    this.layerOrder.push(layer.id);
    this.activeLayerId = layer.id;

    this.eventManager.emit('layer:added', { layer });
    return layer;
  }

  /**
   * 添加图像图层
   */
  addImageLayer(fabricImage: fabric.Image): Layer {
    const fabricBounds = fabricImage.getBoundingRect();
    const bounds = fabricBoundsToRectangle(fabricBounds);
    const layer = this.addLayer(LayerType.IMAGE, {
      fabricObject: fabricImage,
      originalSrc: fabricImage.getSrc()
    });

    layer.bounds = bounds;
    this.updateLayer(layer.id, { bounds });

    return layer;
  }

  /**
   * 添加文本图层
   */
  addTextLayer(text: string, options?: any): Layer {
    const fabricText = new fabric.Text(text, {
      left: 100,
      top: 100,
      fontFamily: 'Arial',
      fontSize: 20,
      fill: '#000000',
      ...options
    });

    this.canvas.add(fabricText);

    const fabricBounds = fabricText.getBoundingRect();
    const bounds = fabricBoundsToRectangle(fabricBounds);
    const layer = this.addLayer(LayerType.TEXT, {
      fabricObject: fabricText,
      text,
      fontFamily: fabricText.fontFamily,
      fontSize: fabricText.fontSize,
      fill: fabricText.fill
    });

    layer.bounds = bounds;
    this.updateLayer(layer.id, { bounds });

    return layer;
  }

  /**
   * 添加形状图层
   */
  addShapeLayer(shapeType: string, options?: any): Layer {
    let fabricShape: fabric.Object;

    switch (shapeType) {
      case 'rectangle':
        fabricShape = new fabric.Rect({
          left: 100,
          top: 100,
          width: 100,
          height: 100,
          fill: '#ff0000',
          ...options
        });
        break;

      case 'circle':
        fabricShape = new fabric.Circle({
          left: 100,
          top: 100,
          radius: 50,
          fill: '#00ff00',
          ...options
        });
        break;

      default:
        throw new Error(`Unsupported shape type: ${shapeType}`);
    }

    this.canvas.add(fabricShape);

    const fabricBounds = fabricShape.getBoundingRect();
    const bounds = fabricBoundsToRectangle(fabricBounds);
    const layer = this.addLayer(LayerType.SHAPE, {
      fabricObject: fabricShape,
      shapeType,
      ...options
    });

    layer.bounds = bounds;
    this.updateLayer(layer.id, { bounds });

    return layer;
  }

  /**
   * 删除图层
   */
  removeLayer(layerId: string): boolean {
    const layer = this.layers.get(layerId);
    if (!layer) return false;

    // 从画布移除对应的 Fabric 对象
    if (layer.data.fabricObject) {
      this.canvas.remove(layer.data.fabricObject);
    }

    // 从图层列表移除
    this.layers.delete(layerId);
    const index = this.layerOrder.indexOf(layerId);
    if (index > -1) {
      this.layerOrder.splice(index, 1);
    }

    // 更新活动图层
    if (this.activeLayerId === layerId) {
      this.activeLayerId =
        this.layerOrder.length > 0 ? this.layerOrder[this.layerOrder.length - 1] : null;
    }

    this.eventManager.emit('layer:removed', { layerId });
    return true;
  }

  /**
   * 更新图层属性
   */
  updateLayer(layerId: string, updates: Partial<Layer>): boolean {
    const layer = this.layers.get(layerId);
    if (!layer) return false;

    // 更新图层属性
    Object.assign(layer, updates, { updatedAt: Date.now() });

    // 同步到 Fabric 对象
    if (layer.data.fabricObject) {
      const fabricObject = layer.data.fabricObject;

      if (updates.visible !== undefined) {
        fabricObject.visible = updates.visible;
      }

      if (updates.opacity !== undefined) {
        fabricObject.opacity = updates.opacity;
      }

      if (updates.locked !== undefined) {
        fabricObject.selectable = !updates.locked;
        fabricObject.evented = !updates.locked;
      }
    }

    this.canvas.renderAll();
    this.eventManager.emit('layer:updated', { layer });
    return true;
  }

  /**
   * 获取图层
   */
  getLayer(layerId: string): Layer | undefined {
    return this.layers.get(layerId);
  }

  /**
   * 获取所有图层
   */
  getAllLayers(): Layer[] {
    return this.layerOrder.map(id => this.layers.get(id)!).filter(Boolean);
  }

  /**
   * 设置活动图层
   */
  setActiveLayer(layerId: string): boolean {
    if (!this.layers.has(layerId)) return false;

    this.activeLayerId = layerId;
    this.eventManager.emit('layer:activated', { layerId });
    return true;
  }

  /**
   * 获取活动图层
   */
  getActiveLayer(): Layer | null {
    return this.activeLayerId ? this.layers.get(this.activeLayerId) || null : null;
  }

  /**
   * 移动图层顺序
   */
  moveLayer(layerId: string, newIndex: number): boolean {
    const currentIndex = this.layerOrder.indexOf(layerId);
    if (currentIndex === -1) return false;

    // 移除并插入到新位置
    this.layerOrder.splice(currentIndex, 1);
    this.layerOrder.splice(newIndex, 0, layerId);

    // 更新画布对象顺序
    this.updateCanvasObjectOrder();

    this.eventManager.emit('layer:moved', { layerId, oldIndex: currentIndex, newIndex });
    return true;
  }

  /**
   * 复制图层
   */
  duplicateLayer(layerId: string): Layer | null {
    const originalLayer = this.layers.get(layerId);
    if (!originalLayer) return null;

    const newLayer: Layer = {
      ...originalLayer,
      id: this.generateLayerId(),
      name: `${originalLayer.name} 副本`,
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    // 复制 Fabric 对象
    if (originalLayer.data.fabricObject) {
      originalLayer.data.fabricObject.clone((clonedObject: fabric.Object) => {
        clonedObject.set({
          left: (clonedObject.left || 0) + 10,
          top: (clonedObject.top || 0) + 10
        });

        this.canvas.add(clonedObject);
        newLayer.data = { ...originalLayer.data, fabricObject: clonedObject };
        newLayer.bounds = fabricBoundsToRectangle(clonedObject.getBoundingRect());

        this.layers.set(newLayer.id, newLayer);
        this.layerOrder.push(newLayer.id);

        this.eventManager.emit('layer:duplicated', { originalLayer, newLayer });
      });
    }

    return newLayer;
  }

  /**
   * 更新画布对象顺序
   */
  private updateCanvasObjectOrder(): void {
    const objects: fabric.Object[] = [];

    // 按图层顺序重新排列对象
    this.layerOrder.forEach(layerId => {
      const layer = this.layers.get(layerId);
      if (layer?.data.fabricObject) {
        objects.push(layer.data.fabricObject);
      }
    });

    // 清除画布并重新添加对象
    this.canvas.clear();
    objects.forEach(obj => this.canvas.add(obj));
    this.canvas.renderAll();
  }

  /**
   * 画布对象事件处理
   */
  private onObjectAdded(_event: fabric.IEvent): void {
    // 当有新对象添加到画布时，检查是否需要创建对应图层
  }

  private onObjectRemoved(_event: fabric.IEvent): void {
    // 当对象从画布移除时，更新对应图层
  }

  private onObjectModified(event: fabric.IEvent): void {
    // 当对象被修改时，更新对应图层的边界信息
    const target = event.target;
    if (target) {
      const layer = this.findLayerByFabricObject(target);
      if (layer) {
        layer.bounds = fabricBoundsToRectangle(target.getBoundingRect());
        layer.updatedAt = Date.now();
        this.eventManager.emit('layer:updated', { layer });
      }
    }
  }

  /**
   * 根据 Fabric 对象查找图层
   */
  private findLayerByFabricObject(fabricObject: fabric.Object): Layer | null {
    for (const layer of this.layers.values()) {
      if (layer.data.fabricObject === fabricObject) {
        return layer;
      }
    }
    return null;
  }

  /**
   * 生成图层 ID
   */
  private generateLayerId(): string {
    return `layer_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 生成图层名称
   */
  private generateLayerName(type: LayerType): string {
    const typeNames = {
      [LayerType.IMAGE]: '图像',
      [LayerType.TEXT]: '文本',
      [LayerType.SHAPE]: '形状',
      [LayerType.ADJUSTMENT]: '调整',
      [LayerType.GROUP]: '组'
    };

    const baseName = typeNames[type] || '图层';
    const existingLayers = Array.from(this.layers.values()).filter(layer =>
      layer.name.startsWith(baseName)
    ).length;

    return existingLayers === 0 ? baseName : `${baseName} ${existingLayers + 1}`;
  }

  /**
   * 销毁图层管理器
   */
  destroy(): void {
    this.layers.clear();
    this.layerOrder = [];
    this.activeLayerId = null;
  }
}
