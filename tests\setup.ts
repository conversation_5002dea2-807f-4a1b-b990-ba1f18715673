// PhotoEditor 2.0 - 测试环境配置
// 配置测试环境和全局测试工具

import { vi } from 'vitest';

// Mock Canvas API
Object.defineProperty(HTMLCanvasElement.prototype, 'getContext', {
  value: vi.fn(() => ({
    fillRect: vi.fn(),
    clearRect: vi.fn(),
    getImageData: vi.fn(() => ({
      data: new Uint8ClampedArray(4)
    })),
    putImageData: vi.fn(),
    createImageData: vi.fn(() => ({
      data: new Uint8ClampedArray(4)
    })),
    setTransform: vi.fn(),
    drawImage: vi.fn(),
    save: vi.fn(),
    fillText: vi.fn(),
    restore: vi.fn(),
    beginPath: vi.fn(),
    moveTo: vi.fn(),
    lineTo: vi.fn(),
    closePath: vi.fn(),
    stroke: vi.fn(),
    translate: vi.fn(),
    scale: vi.fn(),
    rotate: vi.fn(),
    arc: vi.fn(),
    fill: vi.fn(),
    measureText: vi.fn(() => ({ width: 0 })),
    transform: vi.fn(),
    rect: vi.fn(),
    clip: vi.fn()
  }))
});

// Mock HTMLCanvasElement.toDataURL
Object.defineProperty(HTMLCanvasElement.prototype, 'toDataURL', {
  value: vi.fn(() => 'data:image/png;base64,test')
});

// Mock HTMLCanvasElement.toBlob
Object.defineProperty(HTMLCanvasElement.prototype, 'toBlob', {
  value: vi.fn(callback => {
    callback(new Blob(['test'], { type: 'image/png' }));
  })
});

// Mock Image constructor
global.Image = class {
  onload: (() => void) | null = null;
  onerror: (() => void) | null = null;
  src = '';
  width = 100;
  height = 100;

  constructor() {
    setTimeout(() => {
      if (this.onload) {
        this.onload();
      }
    }, 0);
  }
} as any;

// Mock URL.createObjectURL
Object.defineProperty(URL, 'createObjectURL', {
  value: vi.fn(() => 'blob:test-url')
});

Object.defineProperty(URL, 'revokeObjectURL', {
  value: vi.fn()
});

// Mock File API
global.File = class extends Blob {
  name: string;
  lastModified: number;

  constructor(chunks: BlobPart[], filename: string, options?: FilePropertyBag) {
    super(chunks, options);
    this.name = filename;
    this.lastModified = Date.now();
  }
} as any;

// Mock FileReader
global.FileReader = class {
  onload: ((event: any) => void) | null = null;
  onerror: ((event: any) => void) | null = null;
  result: string | ArrayBuffer | null = null;

  readAsDataURL(file: Blob) {
    setTimeout(() => {
      this.result = 'data:image/png;base64,test';
      if (this.onload) {
        this.onload({ target: this });
      }
    }, 0);
  }

  readAsArrayBuffer(file: Blob) {
    setTimeout(() => {
      this.result = new ArrayBuffer(8);
      if (this.onload) {
        this.onload({ target: this });
      }
    }, 0);
  }
} as any;

// Mock ResizeObserver
global.ResizeObserver = class {
  observe() {}
  unobserve() {}
  disconnect() {}
} as any;

// Mock IntersectionObserver
global.IntersectionObserver = class {
  observe() {}
  unobserve() {}
  disconnect() {}
} as any;

// Mock requestAnimationFrame
global.requestAnimationFrame = vi.fn(callback => {
  setTimeout(callback, 16);
  return 1;
});

global.cancelAnimationFrame = vi.fn();

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn()
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

// Mock sessionStorage
Object.defineProperty(window, 'sessionStorage', {
  value: localStorageMock
});

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn()
  }))
});

// Mock Fabric.js
vi.mock('fabric', () => ({
  fabric: {
    Canvas: vi.fn().mockImplementation(() => ({
      add: vi.fn(),
      remove: vi.fn(),
      clear: vi.fn(),
      renderAll: vi.fn(),
      getObjects: vi.fn(() => []),
      getActiveObject: vi.fn(),
      setActiveObject: vi.fn(),
      discardActiveObject: vi.fn(),
      on: vi.fn(),
      off: vi.fn(),
      toJSON: vi.fn(() => ({})),
      loadFromJSON: vi.fn(),
      toDataURL: vi.fn(() => 'data:image/png;base64,test'),
      toBlob: vi.fn(callback => callback(new Blob())),
      setDimensions: vi.fn(),
      getPointer: vi.fn(() => ({ x: 0, y: 0 })),
      zoomToPoint: vi.fn(),
      setViewportTransform: vi.fn(),
      viewportTransform: [1, 0, 0, 1, 0, 0],
      width: 800,
      height: 600,
      backgroundColor: '#ffffff',
      selection: true,
      isDrawingMode: false,
      freeDrawingBrush: {
        width: 10,
        color: '#000000'
      },
      upperCanvasEl: document.createElement('canvas'),
      getElement: vi.fn(() => document.createElement('canvas'))
    })),
    Image: {
      fromURL: vi.fn((url, callback) => {
        const img = {
          src: url,
          width: 100,
          height: 100,
          set: vi.fn(),
          getBoundingRect: vi.fn(() => ({ left: 0, top: 0, width: 100, height: 100 })),
          getSrc: vi.fn(() => url)
        };
        callback(img);
      }),
      filters: {
        Blur: vi.fn(),
        Brightness: vi.fn(),
        Contrast: vi.fn(),
        Saturation: vi.fn(),
        HueRotation: vi.fn(),
        Noise: vi.fn()
      }
    },
    Text: vi.fn().mockImplementation(() => ({
      set: vi.fn(),
      getBoundingRect: vi.fn(() => ({ left: 0, top: 0, width: 100, height: 20 })),
      fontFamily: 'Arial',
      fontSize: 20,
      fill: '#000000'
    })),
    Rect: vi.fn().mockImplementation(() => ({
      set: vi.fn(),
      getBoundingRect: vi.fn(() => ({ left: 0, top: 0, width: 100, height: 100 }))
    })),
    Circle: vi.fn().mockImplementation(() => ({
      set: vi.fn(),
      getBoundingRect: vi.fn(() => ({ left: 0, top: 0, width: 100, height: 100 }))
    })),
    Path: vi.fn().mockImplementation(() => ({
      set: vi.fn(),
      getBoundingRect: vi.fn(() => ({ left: 0, top: 0, width: 100, height: 100 }))
    })),
    Point: vi.fn().mockImplementation((x, y) => ({ x, y }))
  }
}));

// Mock TensorFlow.js
vi.mock('@tensorflow/tfjs', () => ({
  loadLayersModel: vi.fn(),
  tensor: vi.fn(),
  dispose: vi.fn(),
  ready: vi.fn(() => Promise.resolve())
}));

// 全局测试工具函数
export const createMockFile = (name = 'test.png', type = 'image/png') => {
  return new File(['test'], name, { type });
};

export const createMockImage = (width = 100, height = 100) => {
  const img = new Image();
  img.width = width;
  img.height = height;
  return img;
};

export const createMockCanvas = (width = 100, height = 100) => {
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  return canvas;
};

// 清理函数
export const cleanup = () => {
  vi.clearAllMocks();
  document.body.innerHTML = '';
};
