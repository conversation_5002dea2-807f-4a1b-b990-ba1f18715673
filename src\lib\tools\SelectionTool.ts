// PhotoEditor 2.0 - 选择工具
// 负责对象选择和基础变换操作

import { BaseTool } from '$lib/tools/BaseTool';
import { ToolType } from '$lib/types';
import { fabric } from 'fabric';

/**
 * 选择工具
 * 用于选择、移动、缩放和旋转对象
 */
export class SelectionTool extends BaseTool {
  type: ToolType = ToolType.SELECTION;
  name = '选择工具';
  icon = 'cursor-arrow';
  cursor = 'default';

  protected onActivate(): void {
    // 启用对象选择
    this.canvas.selection = true;
    this.canvas.skipTargetFind = false;

    // 设置选择样式
    this.canvas.selectionColor = 'rgba(0, 122, 204, 0.1)';
    this.canvas.selectionBorderColor = '#007acc';
    this.canvas.selectionLineWidth = 1;

    // 启用对象控制
    this.canvas.forEachObject(obj => {
      obj.selectable = true;
      obj.evented = true;
    });

    this.canvas.renderAll();
  }

  protected onDeactivate(): void {
    // 清除选择
    this.canvas.discardActiveObject();
    this.canvas.renderAll();
  }

  protected onOptionsUpdate(): void {
    // 选择工具通常不需要特殊选项
  }

  onMouseDown(event: fabric.IEvent): void {
    // 记录鼠标按下位置，用于拖拽检测
    const pointer = this.canvas.getPointer(event.e);
    this.eventManager.emit('selection:mouse-down', { pointer });
  }

  onMouseUp(_event: fabric.IEvent): void {
    // 检查是否有对象被选中
    const activeObject = this.canvas.getActiveObject();
    if (activeObject) {
      this.eventManager.emit('selection:object-selected', { object: activeObject });
    } else {
      this.eventManager.emit('selection:cleared');
    }
  }

  onKeyDown(event: KeyboardEvent): void {
    const activeObject = this.canvas.getActiveObject();
    if (!activeObject) return;

    switch (event.key) {
      case 'Delete':
      case 'Backspace':
        this.deleteSelectedObject();
        event.preventDefault();
        break;

      case 'ArrowUp':
        this.moveObject(0, -1);
        event.preventDefault();
        break;

      case 'ArrowDown':
        this.moveObject(0, 1);
        event.preventDefault();
        break;

      case 'ArrowLeft':
        this.moveObject(-1, 0);
        event.preventDefault();
        break;

      case 'ArrowRight':
        this.moveObject(1, 0);
        event.preventDefault();
        break;
    }
  }

  /**
   * 删除选中的对象
   */
  private deleteSelectedObject(): void {
    const activeObject = this.canvas.getActiveObject();
    if (activeObject) {
      this.canvas.remove(activeObject);
      this.canvas.renderAll();
      this.eventManager.emit('selection:object-deleted', { object: activeObject });
    }
  }

  /**
   * 移动对象
   */
  private moveObject(deltaX: number, deltaY: number): void {
    const activeObject = this.canvas.getActiveObject();
    if (activeObject) {
      const step = 1; // 移动步长
      activeObject.left! += deltaX * step;
      activeObject.top! += deltaY * step;
      activeObject.setCoords();
      this.canvas.renderAll();
      this.eventManager.emit('selection:object-moved', { object: activeObject });
    }
  }
}
