// PhotoEditor 2.0 - 打码工具
// 负责图像打码/模糊功能 - 项目重点功能

import { BaseTool } from '$lib/tools/BaseTool';
import { MosaicEffect, ToolType } from '$lib/types';
import { fabric } from 'fabric';

/**
 * 打码工具
 * 用于对图像进行打码处理（模糊、马赛克、像素化）
 */
export class MosaicTool extends BaseTool {
  type: ToolType = ToolType.MOSAIC;
  name = '打码工具';
  icon = 'mosaic';
  cursor = 'crosshair';

  private isDrawing = false;
  private mosaicPath: fabric.Path | null = null;
  private pathData: string[] = [];

  protected onActivate(): void {
    // 禁用对象选择
    this.canvas.selection = false;
    this.canvas.forEachObject(obj => {
      obj.selectable = false;
      obj.evented = false;
    });

    // 设置默认打码选项
    if (!this.options.effect) {
      this.options.effect = MosaicEffect.BLUR;
    }
    if (!this.options.intensity) {
      this.options.intensity = 10;
    }
    if (!this.options.size) {
      this.options.size = 20;
    }

    this.eventManager.emit('mosaic:activated');
  }

  protected onDeactivate(): void {
    // 恢复对象选择
    this.canvas.selection = true;
    this.canvas.forEachObject(obj => {
      obj.selectable = true;
      obj.evented = true;
    });

    this.eventManager.emit('mosaic:deactivated');
  }

  protected onOptionsUpdate(): void {
    // 打码选项更新时的处理
    this.eventManager.emit('mosaic:options-updated', { options: this.options });
  }

  onMouseDown(event: fabric.IEvent): void {
    const pointer = this.canvas.getPointer(event.e);
    this.isDrawing = true;
    this.pathData = [`M ${pointer.x} ${pointer.y}`];

    this.eventManager.emit('mosaic:stroke-start', { pointer });
  }

  onMouseMove(event: fabric.IEvent): void {
    if (!this.isDrawing) return;

    const pointer = this.canvas.getPointer(event.e);
    this.pathData.push(`L ${pointer.x} ${pointer.y}`);

    // 实时预览打码效果
    this.updateMosaicPreview();
  }

  onMouseUp(event: fabric.IEvent): void {
    if (!this.isDrawing) return;

    this.isDrawing = false;

    // 应用打码效果
    this.applyMosaicEffect();

    // 清理预览
    this.clearMosaicPreview();

    this.eventManager.emit('mosaic:stroke-end');
  }

  /**
   * 更新打码预览
   */
  private updateMosaicPreview(): void {
    // 清除之前的预览
    this.clearMosaicPreview();

    // 创建路径预览
    const pathString = this.pathData.join(' ');
    this.mosaicPath = new fabric.Path(pathString, {
      fill: 'transparent',
      stroke: 'rgba(255, 0, 0, 0.5)',
      strokeWidth: this.options.size || 20,
      strokeLineCap: 'round',
      strokeLineJoin: 'round',
      selectable: false,
      evented: false
    });

    this.canvas.add(this.mosaicPath);
    this.canvas.renderAll();
  }

  /**
   * 清除打码预览
   */
  private clearMosaicPreview(): void {
    if (this.mosaicPath) {
      this.canvas.remove(this.mosaicPath);
      this.mosaicPath = null;
      this.canvas.renderAll();
    }
  }

  /**
   * 应用打码效果
   */
  private applyMosaicEffect(): void {
    if (this.pathData.length < 2) return;

    const pathString = this.pathData.join(' ');
    const strokeWidth = this.options.size || 20;

    // 创建打码路径
    const mosaicPath = new fabric.Path(pathString, {
      fill: 'transparent',
      stroke: 'transparent',
      strokeWidth: strokeWidth,
      strokeLineCap: 'round',
      strokeLineJoin: 'round',
      selectable: false,
      evented: false
    });

    // 获取路径边界
    const bounds = mosaicPath.getBoundingRect();

    // 扩展边界以包含笔刷宽度
    const expandedBounds = {
      left: bounds.left - strokeWidth / 2,
      top: bounds.top - strokeWidth / 2,
      width: bounds.width + strokeWidth,
      height: bounds.height + strokeWidth
    };

    // 获取该区域的图像数据
    this.getImageDataFromArea(expandedBounds).then(imageData => {
      // 应用打码效果
      const processedImageData = this.processMosaicEffect(imageData, mosaicPath, expandedBounds);

      // 将处理后的图像数据应用回画布
      this.applyProcessedImageData(processedImageData, expandedBounds);
    });

    // 清理路径数据
    this.pathData = [];
  }

  /**
   * 从指定区域获取图像数据
   */
  private async getImageDataFromArea(bounds: any): Promise<ImageData> {
    return new Promise(resolve => {
      const tempCanvas = document.createElement('canvas');
      tempCanvas.width = bounds.width;
      tempCanvas.height = bounds.height;
      const tempCtx = tempCanvas.getContext('2d')!;

      // 将画布内容绘制到临时画布
      const canvasElement = this.canvas.getElement();
      tempCtx.drawImage(
        canvasElement,
        bounds.left,
        bounds.top,
        bounds.width,
        bounds.height,
        0,
        0,
        bounds.width,
        bounds.height
      );

      const imageData = tempCtx.getImageData(0, 0, bounds.width, bounds.height);
      resolve(imageData);
    });
  }

  /**
   * 处理打码效果
   */
  private processMosaicEffect(imageData: ImageData, path: fabric.Path, bounds: any): ImageData {
    const effect = this.options.effect as MosaicEffect;
    const intensity = this.options.intensity || 10;

    switch (effect) {
      case MosaicEffect.BLUR:
        return this.applyBlurEffect(imageData, intensity);

      case MosaicEffect.MOSAIC:
        return this.applyMosaicEffect2(imageData, intensity);

      case MosaicEffect.PIXELATE:
        return this.applyPixelateEffect(imageData, intensity);

      default:
        return imageData;
    }
  }

  /**
   * 应用模糊效果
   */
  private applyBlurEffect(imageData: ImageData, radius: number): ImageData {
    const data = imageData.data;
    const width = imageData.width;
    const height = imageData.height;
    const output = new Uint8ClampedArray(data);

    // 简单的盒式模糊算法
    const boxSize = Math.max(1, Math.floor(radius));

    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        let r = 0,
          g = 0,
          b = 0,
          a = 0;
        let count = 0;

        // 在盒子范围内采样
        for (let dy = -boxSize; dy <= boxSize; dy++) {
          for (let dx = -boxSize; dx <= boxSize; dx++) {
            const nx = x + dx;
            const ny = y + dy;

            if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
              const idx = (ny * width + nx) * 4;
              r += data[idx];
              g += data[idx + 1];
              b += data[idx + 2];
              a += data[idx + 3];
              count++;
            }
          }
        }

        const idx = (y * width + x) * 4;
        output[idx] = r / count;
        output[idx + 1] = g / count;
        output[idx + 2] = b / count;
        output[idx + 3] = a / count;
      }
    }

    return new ImageData(output, width, height);
  }

  /**
   * 应用马赛克效果
   */
  private applyMosaicEffect2(imageData: ImageData, blockSize: number): ImageData {
    const data = imageData.data;
    const width = imageData.width;
    const height = imageData.height;
    const output = new Uint8ClampedArray(data);

    const size = Math.max(2, Math.floor(blockSize));

    for (let y = 0; y < height; y += size) {
      for (let x = 0; x < width; x += size) {
        // 计算块的平均颜色
        let r = 0,
          g = 0,
          b = 0,
          a = 0;
        let count = 0;

        for (let dy = 0; dy < size && y + dy < height; dy++) {
          for (let dx = 0; dx < size && x + dx < width; dx++) {
            const idx = ((y + dy) * width + (x + dx)) * 4;
            r += data[idx];
            g += data[idx + 1];
            b += data[idx + 2];
            a += data[idx + 3];
            count++;
          }
        }

        r = Math.floor(r / count);
        g = Math.floor(g / count);
        b = Math.floor(b / count);
        a = Math.floor(a / count);

        // 将平均颜色应用到整个块
        for (let dy = 0; dy < size && y + dy < height; dy++) {
          for (let dx = 0; dx < size && x + dx < width; dx++) {
            const idx = ((y + dy) * width + (x + dx)) * 4;
            output[idx] = r;
            output[idx + 1] = g;
            output[idx + 2] = b;
            output[idx + 3] = a;
          }
        }
      }
    }

    return new ImageData(output, width, height);
  }

  /**
   * 应用像素化效果
   */
  private applyPixelateEffect(imageData: ImageData, pixelSize: number): ImageData {
    // 像素化效果类似马赛克，但使用更大的块
    return this.applyMosaicEffect2(imageData, pixelSize * 2);
  }

  /**
   * 将处理后的图像数据应用回画布
   */
  private applyProcessedImageData(imageData: ImageData, bounds: any): void {
    const tempCanvas = document.createElement('canvas');
    tempCanvas.width = bounds.width;
    tempCanvas.height = bounds.height;
    const tempCtx = tempCanvas.getContext('2d')!;

    tempCtx.putImageData(imageData, 0, 0);

    fabric.Image.fromURL(tempCanvas.toDataURL(), img => {
      img.set({
        left: bounds.left,
        top: bounds.top,
        selectable: false,
        evented: false
      });

      this.canvas.add(img);
      this.canvas.renderAll();

      this.eventManager.emit('mosaic:effect-applied', {
        bounds,
        effect: this.options.effect
      });
    });
  }

  /**
   * 设置打码效果类型
   */
  setMosaicEffect(effect: MosaicEffect): void {
    this.updateOptions({ effect });
  }

  /**
   * 设置打码强度
   */
  setMosaicIntensity(intensity: number): void {
    this.updateOptions({ intensity });
  }

  /**
   * 设置打码笔刷大小
   */
  setMosaicSize(size: number): void {
    this.updateOptions({ size });
  }
}
