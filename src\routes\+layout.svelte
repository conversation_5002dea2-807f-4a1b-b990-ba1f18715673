<script lang="ts">
  import '../app.css';
  import { onMount } from 'svelte';
  import { browser } from '$app/environment';

  // 全局错误处理
  if (browser) {
    window.addEventListener('error', event => {
      console.error('Global error:', event.error);
      // 这里可以添加错误上报逻辑
    });

    window.addEventListener('unhandledrejection', event => {
      console.error('Unhandled promise rejection:', event.reason);
      // 这里可以添加错误上报逻辑
    });
  }

  onMount(() => {
    // 检查浏览器兼容性
    if (!window.HTMLCanvasElement) {
      alert('您的浏览器不支持 Canvas，请升级到现代浏览器');
      return;
    }

    // 检查 WebGL 支持
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    if (!gl) {
      console.warn('WebGL not supported, some features may be limited');
    }

    // 设置主题
    const theme = localStorage.getItem('theme') || 'dark';
    document.documentElement.classList.toggle('dark', theme === 'dark');
  });
</script>

<svelte:head>
  <title>PhotoEditor 2.0 - 专业在线图片编辑器</title>
</svelte:head>

<main class="min-h-screen bg-editor-bg">
  <slot />
</main>

<style>
  :global(html) {
    font-family:
      'Inter',
      -apple-system,
      BlinkMacSystemFont,
      'Segoe UI',
      Roboto,
      sans-serif;
  }

  :global(body) {
    margin: 0;
    padding: 0;
    overflow: hidden;
  }

  :global(*) {
    box-sizing: border-box;
  }

  :global(.no-scrollbar) {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  :global(.no-scrollbar::-webkit-scrollbar) {
    display: none;
  }
</style>
