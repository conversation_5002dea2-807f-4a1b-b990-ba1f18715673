{"name": "photoeditor", "version": "0.1.0", "description": "PhotoEditor - 基于 Svelte + TypeScript 的专业在线图片编辑器", "author": "luo<PERSON>an", "private": true, "type": "module", "keywords": ["photo-editor", "image-editor", "svelte", "typescript", "canvas", "fabric.js", "web-app"], "repository": {"type": "git", "url": "https://github.com/luoleyan/PhotoEditor.git"}, "homepage": "https://github.com/luoleyan/PhotoEditor", "bugs": {"url": "https://github.com/luoleyan/PhotoEditor/issues"}, "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:e2e": "playwright test", "test:coverage": "vitest run --coverage", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "prepare": "husky install", "validate": "npm run type-check && npm run lint && npm run test && npm run build"}, "devDependencies": {"@playwright/test": "^1.40.0", "@sveltejs/adapter-auto": "^3.0.0", "@sveltejs/adapter-static": "^3.0.0", "@sveltejs/adapter-vercel": "^4.0.0", "@sveltejs/kit": "^2.0.0", "@sveltejs/vite-plugin-svelte": "^3.0.0", "@testing-library/jest-dom": "^6.1.0", "@testing-library/svelte": "^4.0.0", "@types/lodash-es": "^4.17.12", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitest/coverage-v8": "^1.0.0", "@vitest/ui": "^1.0.0", "autoprefixer": "^10.4.0", "cssnano": "^6.0.0", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-svelte": "^2.46.1", "husky": "^8.0.3", "jsdom": "^23.0.0", "lint-staged": "^15.2.0", "postcss": "^8.4.0", "prettier": "^3.1.0", "prettier-plugin-svelte": "^3.1.0", "svelte": "^4.2.8", "svelte-check": "^3.6.0", "tailwindcss": "^3.4.0", "typescript": "^5.3.0", "vite": "^5.0.0", "vitest": "^1.0.0"}, "dependencies": {"@tensorflow/tfjs": "^4.15.0", "@tensorflow/tfjs-backend-webgl": "^4.15.0", "date-fns": "^3.0.0", "fabric": "^5.3.0", "face-api.js": "^0.22.2", "konva": "^9.2.0", "lodash-es": "^4.17.21"}, "lint-staged": {"*.{js,ts,svelte}": ["eslint --fix", "prettier --write"], "*.{css,md,json}": ["prettier --write"]}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.15.0"}