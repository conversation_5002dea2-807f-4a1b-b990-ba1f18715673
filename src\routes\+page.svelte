<script lang="ts">
  import { browser } from '$app/environment';
  import { PhotoEditor } from '$lib/core/Editor';
  import { ToolType, type EditorOptions } from '$lib/types';
  import { onMount } from 'svelte';

  let editorContainer: HTMLElement;
  let editor: PhotoEditor | null = null;
  let isLoading = true;
  let error: string | null = null;

  onMount(() => {
    if (!browser) return;

    const initializeEditor = async () => {
      try {
        // 等待 DOM 更新完成
        await new Promise(resolve => setTimeout(resolve, 0));

        // 确保 DOM 元素已经绑定
        if (!editorContainer) {
          throw new Error('编辑器容器元素未找到');
        }

        // 检查容器元素是否已添加到 DOM
        if (!editorContainer.parentNode) {
          throw new Error('编辑器容器元素未添加到 DOM');
        }

        console.log('Initializing PhotoEditor with container:', editorContainer);

        // 初始化编辑器
        const options: Partial<EditorOptions> = {
          width: window.innerWidth - 300, // 减去侧边栏宽度
          height: window.innerHeight - 100, // 减去工具栏高度
          backgroundColor: '#ffffff',
          enableTouch: true,
          enableKeyboard: true,
          maxHistorySteps: 50
        };

        editor = new PhotoEditor(editorContainer, options);

        // 监听窗口大小变化
        const handleResize = () => {
          if (editor) {
            const canvas = editor.getCanvas();
            canvas.setDimensions({
              width: window.innerWidth - 300,
              height: window.innerHeight - 100
            });
          }
        };

        window.addEventListener('resize', handleResize);

        isLoading = false;

        // 返回清理函数
        return () => {
          window.removeEventListener('resize', handleResize);
          if (editor) {
            editor.destroy();
          }
        };
      } catch (err) {
        console.error('PhotoEditor initialization failed:', err);
        error = err instanceof Error ? err.message : '编辑器初始化失败';
        isLoading = false;
        return undefined;
      }
    };

    // 执行异步初始化并处理清理函数
    const cleanupPromise = initializeEditor();

    return () => {
      cleanupPromise.then(cleanup => {
        if (cleanup) {
          cleanup();
        }
      });
    };
  });

  // 处理文件拖拽
  function handleDrop(event: DragEvent) {
    event.preventDefault();
    const files = event.dataTransfer?.files;
    if (files && files.length > 0 && editor) {
      const file = files[0];
      if (file.type.startsWith('image/')) {
        editor.loadImage(file);
      }
    }
  }

  function handleDragOver(event: DragEvent) {
    event.preventDefault();
  }

  // 处理文件选择
  function handleFileSelect(event: Event) {
    const input = event.target as HTMLInputElement;
    const file = input.files?.[0];
    if (file && editor) {
      editor.loadImage(file);
    }
  }
</script>

<svelte:head>
  <title>PhotoEditor 2.0 - 在线图片编辑器</title>
  <meta name="description" content="专业的在线图片编辑器，支持图像打码、滤镜效果、AI功能等" />
</svelte:head>

<div class="h-screen flex flex-col bg-editor-bg text-editor-text">
  <!-- 顶部工具栏 -->
  <header class="h-16 bg-toolbar-bg border-b border-toolbar-border flex items-center px-4">
    <div class="flex items-center space-x-4">
      <h1 class="text-xl font-bold text-editor-text">PhotoEditor 2.0</h1>

      <!-- 文件操作 -->
      <div class="flex items-center space-x-2">
        <label
          class="px-3 py-1.5 bg-editor-accent text-white rounded cursor-pointer hover:bg-editor-accent-hover transition-colors"
        >
          <span>打开图片</span>
          <input type="file" accept="image/*" class="hidden" on:change={handleFileSelect} />
        </label>

        <button
          class="px-3 py-1.5 bg-toolbar-hover text-editor-text rounded hover:bg-toolbar-active transition-colors"
          disabled={!editor}
          on:click={() => editor?.saveProject()}
        >
          保存项目
        </button>
      </div>
    </div>

    <!-- 右侧工具 -->
    <div class="ml-auto flex items-center space-x-2">
      <button
        class="p-2 hover:bg-toolbar-hover rounded transition-colors"
        disabled={!editor}
        on:click={() => editor?.undo()}
        title="撤销 (Ctrl+Z)"
      >
        ↶
      </button>

      <button
        class="p-2 hover:bg-toolbar-hover rounded transition-colors"
        disabled={!editor}
        on:click={() => editor?.redo()}
        title="重做 (Ctrl+Shift+Z)"
      >
        ↷
      </button>
    </div>
  </header>

  <!-- 主要内容区域 -->
  <div class="flex-1 flex">
    <!-- 左侧工具面板 -->
    <aside
      class="w-16 bg-toolbar-bg border-r border-toolbar-border flex flex-col items-center py-4 space-y-2"
    >
      <button
        class="w-10 h-10 flex items-center justify-center rounded hover:bg-toolbar-hover transition-colors"
        title="选择工具"
        on:click={() => editor?.selectTool(ToolType.SELECTION)}
      >
        🔍
      </button>

      <button
        class="w-10 h-10 flex items-center justify-center rounded hover:bg-toolbar-hover transition-colors"
        title="画笔工具"
        on:click={() => editor?.selectTool(ToolType.BRUSH)}
      >
        🖌️
      </button>

      <button
        class="w-10 h-10 flex items-center justify-center rounded hover:bg-toolbar-hover transition-colors"
        title="裁剪工具"
        on:click={() => editor?.selectTool(ToolType.CROP)}
      >
        ✂️
      </button>

      <button
        class="w-10 h-10 flex items-center justify-center rounded hover:bg-toolbar-hover transition-colors"
        title="打码工具"
        on:click={() => editor?.selectTool(ToolType.MOSAIC)}
      >
        🔲
      </button>
    </aside>

    <!-- 编辑器画布区域 -->
    <main class="flex-1 relative overflow-hidden" on:drop={handleDrop} on:dragover={handleDragOver}>
      {#if isLoading}
        <div class="absolute inset-0 flex items-center justify-center bg-editor-bg">
          <div class="text-center">
            <div
              class="animate-spin rounded-full h-12 w-12 border-b-2 border-editor-accent mx-auto mb-4"
            ></div>
            <p class="text-editor-text-secondary">正在初始化编辑器...</p>
          </div>
        </div>
      {:else if error}
        <div class="absolute inset-0 flex items-center justify-center bg-editor-bg">
          <div class="text-center text-red-500">
            <p class="text-xl mb-2">❌</p>
            <p>编辑器加载失败</p>
            <p class="text-sm text-editor-text-secondary mt-2">{error}</p>
          </div>
        </div>
      {:else}
        <!-- 编辑器容器 -->
        <div bind:this={editorContainer} class="w-full h-full bg-gray-100"></div>

        <!-- 拖拽提示 -->
        <div class="absolute inset-0 pointer-events-none flex items-center justify-center">
          <div class="text-center text-editor-text-secondary opacity-50">
            <p class="text-2xl mb-2">📷</p>
            <p>拖拽图片到此处开始编辑</p>
            <p class="text-sm mt-1">或点击"打开图片"按钮</p>
          </div>
        </div>
      {/if}
    </main>

    <!-- 右侧属性面板 -->
    <aside class="w-64 bg-editor-panel border-l border-editor-border">
      <div class="p-4">
        <h3 class="text-lg font-semibold mb-4">属性面板</h3>

        <!-- 图层面板 -->
        <div class="mb-6">
          <h4 class="text-sm font-medium mb-2">图层</h4>
          <div class="space-y-1">
            <div class="p-2 bg-toolbar-hover rounded text-sm">背景图层</div>
          </div>
        </div>

        <!-- 工具选项 -->
        <div class="mb-6">
          <h4 class="text-sm font-medium mb-2">工具选项</h4>
          <div class="space-y-2">
            <div>
              <label for="brush-size" class="block text-xs text-editor-text-secondary mb-1"
                >画笔大小</label
              >
              <input id="brush-size" type="range" min="1" max="100" value="10" class="w-full" />
            </div>

            <div>
              <label for="opacity" class="block text-xs text-editor-text-secondary mb-1"
                >透明度</label
              >
              <input id="opacity" type="range" min="0" max="100" value="100" class="w-full" />
            </div>
          </div>
        </div>

        <!-- 滤镜面板 -->
        <div>
          <h4 class="text-sm font-medium mb-2">滤镜效果</h4>
          <div class="grid grid-cols-2 gap-2">
            <button
              class="p-2 bg-toolbar-hover rounded text-xs hover:bg-toolbar-active transition-colors"
            >
              模糊
            </button>
            <button
              class="p-2 bg-toolbar-hover rounded text-xs hover:bg-toolbar-active transition-colors"
            >
              锐化
            </button>
            <button
              class="p-2 bg-toolbar-hover rounded text-xs hover:bg-toolbar-active transition-colors"
            >
              亮度
            </button>
            <button
              class="p-2 bg-toolbar-hover rounded text-xs hover:bg-toolbar-active transition-colors"
            >
              对比度
            </button>
          </div>
        </div>
      </div>
    </aside>
  </div>
</div>
