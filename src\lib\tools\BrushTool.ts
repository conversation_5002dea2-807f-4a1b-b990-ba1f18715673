// PhotoEditor 2.0 - 画笔工具
// 负责自由绘画功能

import { BaseTool } from '$lib/tools/BaseTool';
import { ToolType } from '$lib/types';
import { fabric } from 'fabric';

/**
 * 画笔工具
 * 用于自由绘画
 */
export class BrushTool extends BaseTool {
  type: ToolType = ToolType.BRUSH;
  name = '画笔工具';
  icon = 'brush';
  cursor = 'crosshair';

  protected onActivate(): void {
    // 启用绘画模式
    this.canvas.isDrawingMode = true;

    // 禁用对象选择
    this.canvas.selection = false;
    this.canvas.forEachObject(obj => {
      obj.selectable = false;
      obj.evented = false;
    });

    // 设置画笔属性
    this.updateBrushSettings();
  }

  protected onDeactivate(): void {
    // 禁用绘画模式
    this.canvas.isDrawingMode = false;

    // 恢复对象选择
    this.canvas.selection = true;
    this.canvas.forEachObject(obj => {
      obj.selectable = true;
      obj.evented = true;
    });
  }

  protected onOptionsUpdate(): void {
    this.updateBrushSettings();
  }

  /**
   * 更新画笔设置
   */
  private updateBrushSettings(): void {
    if (!this.canvas.freeDrawingBrush) return;

    const brush = this.canvas.freeDrawingBrush;

    // 设置画笔宽度
    brush.width = this.options.size || 10;

    // 设置画笔颜色
    if (this.options.color) {
      const { r, g, b, a = 1 } = this.options.color;
      brush.color = `rgba(${r}, ${g}, ${b}, ${a})`;
    }

    // 设置画笔透明度
    if (this.options.opacity !== undefined) {
      // Fabric.js 的画笔透明度通过颜色的 alpha 通道控制
      if (this.options.color) {
        const { r, g, b } = this.options.color;
        brush.color = `rgba(${r}, ${g}, ${b}, ${this.options.opacity})`;
      }
    }
  }

  onMouseDown(event: fabric.IEvent): void {
    this.eventManager.emit('brush:stroke-start', {
      pointer: this.canvas.getPointer(event.e)
    });
  }

  onMouseUp(event: fabric.IEvent): void {
    this.eventManager.emit('brush:stroke-end', {
      pointer: this.canvas.getPointer(event.e)
    });
  }
}
