# PhotoEditor - Git Ignore File
# 作者: luoleyan
# 创建时间: 2025年7月17日

# ============================================================================
# 依赖目录
# ============================================================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# ============================================================================
# 构建产物
# ============================================================================
dist/
build/
.output/
.vercel/
.netlify/
.svelte-kit/

# ============================================================================
# 开发工具缓存
# ============================================================================
.vite/
.cache/
.parcel-cache/
.nyc_output/
coverage/
*.lcov

# ============================================================================
# 测试相关
# ============================================================================
test-results/
playwright-report/
playwright/.cache/
coverage/
*.coverage
.nyc_output

# ============================================================================
# 日志文件
# ============================================================================
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# ============================================================================
# 运行时文件
# ============================================================================
pids/
*.pid
*.seed
*.pid.lock

# ============================================================================
# 环境变量文件
# ============================================================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# ============================================================================
# IDE 和编辑器文件
# ============================================================================
# VSCode (保留项目配置)
# .vscode/

# JetBrains IDEs
.idea/
*.swp
*.swo
*~

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*.swn

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ============================================================================
# 操作系统文件
# ============================================================================
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ============================================================================
# 临时文件
# ============================================================================
*.tmp
*.temp
*.swp
*.swo
*.bak
*.backup
*.orig
*.rej

# ============================================================================
# 压缩文件
# ============================================================================
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip
*.tgz
*.tar.gz

# ============================================================================
# 项目特定文件
# ============================================================================
# 用户上传的图片（如果有本地存储）
uploads/
temp-images/
processed-images/

# 模型文件（AI 相关）
*.model
*.weights
*.h5
*.pb
models/temp/

# 性能分析文件
*.cpuprofile
*.heapprofile
*.heapsnapshot

# ============================================================================
# 其他
# ============================================================================
.eslintcache
.stylelintcache
*.tsbuildinfo
.npm
.yarn-integrity
.pnp.*
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.js
