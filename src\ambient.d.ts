// PhotoEditor 2.0 - 全局类型声明
// 为第三方库和全局变量提供类型定义

/// <reference types="svelte" />
/// <reference types="vite/client" />

// 全局常量
declare const __APP_VERSION__: string;
declare const __BUILD_TIME__: string;

// 环境变量
interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string;
  readonly VITE_API_BASE_URL: string;
  readonly VITE_AI_SERVICE_URL: string;
  readonly VITE_MAX_FILE_SIZE: string;
  readonly VITE_ALLOWED_FILE_TYPES: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

// SvelteKit $app/environment 模块类型定义
declare module '$app/environment' {
  export const browser: boolean;
  export const dev: boolean;
  export const building: boolean;
  export const version: string;
}

// Fabric.js 类型扩展
declare module 'fabric' {
  namespace fabric {
    // 构造函数
    class Canvas {
      constructor(element: HTMLCanvasElement | string, options?: any);
      upperCanvasEl: HTMLCanvasElement;
      lowerCanvasEl: HTMLCanvasElement;
      wrapperEl: HTMLDivElement;
      getElement(): HTMLCanvasElement;
      toBlob(callback: (blob: Blob | null) => void, format?: string, quality?: number): void;
      isDrawingMode: boolean;
      freeDrawingBrush: { width: number; color: string };
      selection: boolean;
      forEachObject(callback: (obj: Object) => void): void;
      getObjects(): Object[];
      getActiveObject(): Object | null;
      setActiveObject(obj: Object): void;
      discardActiveObject(): void;
      add(obj: Object): void;
      remove(obj: Object): void;
      clear(): void;
      renderAll(): void;
      on(eventName: string, handler: (event: IEvent) => void): void;
      off(eventName: string, handler?: (event: IEvent) => void): void;
      getPointer(e: Event): Point;
      zoomToPoint(point: Point | undefined, zoom: number): void;
      setViewportTransform(transform: number[]): void;
      viewportTransform: number[];
      width?: number;
      height?: number;
      backgroundColor?: string;
      toJSON(): any;
      loadFromJSON(json: any, callback?: () => void): void;
      toDataURL(
        options?:
          | string
          | {
              format?: string;
              quality?: number;
              multiplier?: number;
              left?: number;
              top?: number;
              width?: number;
              height?: number;
            }
      ): string;
      setDimensions(dimensions: { width: number; height: number }): void;
      centerObject(obj: Object): void;
      dispose(): void;

      // 选择相关属性
      skipTargetFind: boolean;
      selectionColor: string;
      selectionBorderColor: string;
      selectionLineWidth: number;

      // 光标相关属性
      defaultCursor: string;
      hoverCursor: string;
      moveCursor: string;
    }

    class Image {
      constructor(element: HTMLImageElement | string, options?: any);
      static fromURL(url: string, callback: (img: Image) => void, options?: any): void;
      static fromObject(object: any, callback: (img: Image) => void): void;

      // 继承自 Object 的属性
      canvas?: Canvas;
      group?: Group;
      left?: number;
      top?: number;
      width?: number;
      height?: number;
      scaleX?: number;
      scaleY?: number;
      angle?: number;
      selectable?: boolean;
      evented?: boolean;
      visible?: boolean;
      opacity?: number;
      filters?: IBaseFilter[];

      // 继承自 Object 的方法
      set(options: any): Object;
      get(property: string): any;
      setCoords(): void;
      getBoundingRect(): { left: number; top: number; width: number; height: number };
      clone(callback?: (obj: Object) => void): void;
      applyFilters(): void;

      // Image 特有的属性和方法
      src: string;
      getSrc(): string;
      setSrc(src: string, callback?: () => void): void;
      getElement(): HTMLImageElement;
    }

    class Point {
      constructor(x: number, y: number);
      x: number;
      y: number;
      add(point: Point): Point;
      subtract(point: Point): Point;
      multiply(scalar: number): Point;
      divide(scalar: number): Point;
      equals(point: Point): boolean;
    }

    class Text {
      constructor(text: string, options?: any);
      text: string;
      fontFamily?: string;
      fontSize?: number;
      fill?: string;
      // 继承自 Object 的属性和方法
      canvas?: Canvas;
      group?: Group;
      left?: number;
      top?: number;
      width?: number;
      height?: number;
      scaleX?: number;
      scaleY?: number;
      angle?: number;
      selectable?: boolean;
      evented?: boolean;
      visible?: boolean;
      opacity?: number;
      filters?: IBaseFilter[];
      set(options: any): Object;
      get(property: string): any;
      setCoords(): void;
      getBoundingRect(): { left: number; top: number; width: number; height: number };
      clone(callback?: (obj: Object) => void): void;
      applyFilters(): void;
    }

    class Rect {
      constructor(options?: any);
      // 继承自 Object 的属性和方法
      canvas?: Canvas;
      group?: Group;
      left?: number;
      top?: number;
      width?: number;
      height?: number;
      scaleX?: number;
      scaleY?: number;
      angle?: number;
      selectable?: boolean;
      evented?: boolean;
      visible?: boolean;
      opacity?: number;
      filters?: IBaseFilter[];
      fill?: string;
      stroke?: string;
      strokeWidth?: number;
      set(options: any): Object;
      get(property: string): any;
      setCoords(): void;
      getBoundingRect(): { left: number; top: number; width: number; height: number };
      clone(callback?: (obj: Object) => void): void;
      applyFilters(): void;
    }

    class Circle {
      constructor(options?: any);
      radius?: number;
      // 继承自 Object 的属性和方法
      canvas?: Canvas;
      group?: Group;
      left?: number;
      top?: number;
      width?: number;
      height?: number;
      scaleX?: number;
      scaleY?: number;
      angle?: number;
      selectable?: boolean;
      evented?: boolean;
      visible?: boolean;
      opacity?: number;
      filters?: IBaseFilter[];
      fill?: string;
      stroke?: string;
      strokeWidth?: number;
      set(options: any): Object;
      get(property: string): any;
      setCoords(): void;
      getBoundingRect(): { left: number; top: number; width: number; height: number };
      clone(callback?: (obj: Object) => void): void;
      applyFilters(): void;
    }

    class Path {
      constructor(path: string | string[], options?: any);
      static fromObject(object: any, callback: (path: Path) => void): void;

      // Path 特有的属性
      path?: string | string[];
      pathOffset?: Point;

      // 继承自 Object 的属性和方法
      canvas?: Canvas;
      group?: Group;
      left?: number;
      top?: number;
      width?: number;
      height?: number;
      scaleX?: number;
      scaleY?: number;
      angle?: number;
      selectable?: boolean;
      evented?: boolean;
      visible?: boolean;
      opacity?: number;
      filters?: IBaseFilter[];
      fill?: string;
      stroke?: string;
      strokeWidth?: number;
      strokeLineCap?: string;
      strokeLineJoin?: string;
      strokeDashArray?: number[];

      // 继承自 Object 的方法
      set(options: any): Object;
      get(property: string): any;
      setCoords(): void;
      getBoundingRect(): { left: number; top: number; width: number; height: number };
      clone(callback?: (obj: Object) => void): void;
      applyFilters(): void;

      // Path 特有的方法
      getPath(): string;
      setPath(path: string): void;
      complexity(): number;
      toSVG(): string;
    }
    interface IEvent {
      e: Event;
      target?: Object;
      subTargets?: Object[];
      button?: number;
      isClick?: boolean;
      pointer?: Point;
      absolutePointer?: Point;
      transform?: {
        corner: string;
        original: Object;
        originX: string;
        originY: string;
        width: number;
        height: number;
      };
    }

    interface Object {
      canvas?: Canvas;
      group?: Group;

      // 基础属性
      left?: number;
      top?: number;
      width?: number;
      height?: number;
      scaleX?: number;
      scaleY?: number;
      angle?: number;

      // 交互属性
      selectable?: boolean;
      evented?: boolean;
      visible?: boolean;
      opacity?: number;

      // 方法
      set(options: any): Object;
      get(property: string): any;
      setCoords(): void;
      getBoundingRect(): { left: number; top: number; width: number; height: number };
      clone(callback?: (obj: Object) => void): void;

      // 滤镜相关
      filters?: IBaseFilter[];
      applyFilters(): void;

      // 图像相关（针对 fabric.Image）
      getSrc?(): string;
      getElement?(): HTMLImageElement;
      setSrc?(src: string, callback?: () => void): void;
    }

    interface IBaseFilter {
      type: string;
      [key: string]: any;
    }

    namespace Image {
      const filters: {
        Blur: new (options?: any) => IBaseFilter;
        Brightness: new (options?: any) => IBaseFilter;
        Contrast: new (options?: any) => IBaseFilter;
        Saturation: new (options?: any) => IBaseFilter;
        HueRotation: new (options?: any) => IBaseFilter;
        Noise: new (options?: any) => IBaseFilter;
        [key: string]: new (options?: any) => IBaseFilter;
      };
    }
  }
}

// Web APIs 扩展
interface HTMLCanvasElement {
  toBlob(callback: (blob: Blob | null) => void, type?: string, quality?: any): void;
}

// 文件处理相关
interface FileWithPath extends File {
  path?: string;
}

// 拖拽事件类型
interface DragEvent {
  dataTransfer: DataTransfer | null;
}

// 触摸事件类型
interface TouchEvent {
  touches: TouchList;
  targetTouches: TouchList;
  changedTouches: TouchList;
}

// 性能监控
interface Performance {
  memory?: {
    usedJSHeapSize: number;
    totalJSHeapSize: number;
    jsHeapSizeLimit: number;
  };
}

// Web Workers
declare module '*.worker.ts' {
  class WebpackWorker extends Worker {
    constructor();
  }
  export default WebpackWorker;
}

// CSS Modules
declare module '*.module.css' {
  const classes: { [key: string]: string };
  export default classes;
}

// 图像文件
declare module '*.png' {
  const src: string;
  export default src;
}

declare module '*.jpg' {
  const src: string;
  export default src;
}

declare module '*.jpeg' {
  const src: string;
  export default src;
}

declare module '*.gif' {
  const src: string;
  export default src;
}

declare module '*.webp' {
  const src: string;
  export default src;
}

declare module '*.svg' {
  const src: string;
  export default src;
}

// 字体文件
declare module '*.woff' {
  const src: string;
  export default src;
}

declare module '*.woff2' {
  const src: string;
  export default src;
}

// AI 模型文件
declare module '*.json' {
  const value: any;
  export default value;
}

// TensorFlow.js 类型扩展
declare module '@tensorflow/tfjs' {
  export interface Tensor {
    dispose(): void;
  }
}

// 浏览器兼容性检查
interface Navigator {
  deviceMemory?: number;
  connection?: {
    effectiveType: string;
    downlink: number;
    rtt: number;
  };
}

// 错误边界
interface ErrorInfo {
  componentStack: string;
}

// 全局错误处理
interface Window {
  __PHOTOEDITOR_DEBUG__?: boolean;
  __PHOTOEDITOR_VERSION__?: string;
}

// Svelte 组件类型
declare namespace svelte.JSX {
  interface HTMLAttributes<T> {
    'on:click_outside'?: (event: CustomEvent) => void;
    'on:long_press'?: (event: CustomEvent) => void;
    'on:swipe'?: (event: CustomEvent) => void;
  }
}
