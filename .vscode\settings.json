{"typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "files.associations": {"*.svelte": "svelte"}, "emmet.includeLanguages": {"svelte": "html"}, "svelte.enable-ts-plugin": true, "search.exclude": {"**/node_modules": true, "**/.svelte-kit": true, "**/build": true, "**/dist": true}, "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/node_modules": true, "**/.svelte-kit": true}, "editor.tabSize": 2, "editor.insertSpaces": true, "editor.detectIndentation": false, "css.validate": false, "less.validate": false, "scss.validate": false, "tailwindCSS.includeLanguages": {"svelte": "html"}, "tailwindCSS.classAttributes": ["class", "className", "ngClass"]}