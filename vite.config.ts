import { sveltekit } from '@sveltejs/kit/vite';
import { defineConfig } from 'vite';

export default defineConfig({
  plugins: [sveltekit()],

  // 依赖优化配置
  optimizeDeps: {
    include: [
      'fabric',
      'konva',
      '@tensorflow/tfjs',
      '@tensorflow/tfjs-backend-webgl',
      'lodash-es',
      'date-fns'
    ],
    exclude: ['@sveltejs/kit']
  },

  // 构建配置
  build: {
    target: 'es2020',
    sourcemap: true,
    rollupOptions: {
      output: {
        // 代码分割策略
        manualChunks: id => {
          if (id.includes('node_modules')) {
            if (id.includes('fabric')) {
              return 'image-processing';
            }
            if (id.includes('@tensorflow') || id.includes('face-api')) {
              return 'ai-features';
            }
            if (id.includes('lodash') || id.includes('date-fns')) {
              return 'utils';
            }
            return 'vendor';
          }
        }
      }
    },
    // 增加chunk大小警告阈值
    chunkSizeWarningLimit: 1000
  },

  // Web Workers 支持
  worker: {
    format: 'es'
  },

  // 开发服务器配置
  server: {
    fs: {
      allow: ['..']
    },
    port: 5173,
    host: true
  },

  // 预览服务器配置
  preview: {
    port: 4173,
    host: true
  },

  // 定义全局常量
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
    __BUILD_TIME__: JSON.stringify(new Date().toISOString())
  }
});
