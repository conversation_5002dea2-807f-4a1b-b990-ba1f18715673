@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* 自定义字体 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* 基础样式重置 */
@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    @apply bg-editor-bg text-editor-text;
    margin: 0;
    padding: 0;
    overflow: hidden;
  }

  * {
    box-sizing: border-box;
  }

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-editor-panel;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-toolbar-border rounded;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-toolbar-hover;
  }
}

/* 组件样式 */
@layer components {
  /* 按钮样式 */
  .btn {
    @apply px-4 py-2 rounded font-medium transition-colors duration-200;
  }

  .btn-primary {
    @apply bg-editor-accent text-white hover:bg-editor-accent-hover;
  }

  .btn-secondary {
    @apply bg-toolbar-hover text-editor-text hover:bg-toolbar-active;
  }

  .btn-ghost {
    @apply text-editor-text hover:bg-toolbar-hover;
  }

  /* 输入框样式 */
  .input {
    @apply w-full px-3 py-2 bg-editor-panel border border-toolbar-border rounded;
    @apply text-editor-text placeholder-editor-text-secondary;
    @apply focus:outline-none focus:ring-2 focus:ring-editor-accent focus:border-transparent;
  }

  /* 面板样式 */
  .panel {
    @apply bg-editor-panel border border-editor-border rounded-lg;
  }

  .panel-header {
    @apply px-4 py-3 border-b border-editor-border font-medium;
  }

  .panel-content {
    @apply p-4;
  }

  /* 工具栏样式 */
  .toolbar {
    @apply bg-toolbar-bg border-b border-toolbar-border;
  }

  .toolbar-item {
    @apply p-2 rounded hover:bg-toolbar-hover transition-colors duration-200;
  }

  .toolbar-item.active {
    @apply bg-toolbar-active text-editor-accent;
  }

  /* 图层样式 */
  .layer-item {
    @apply p-2 rounded cursor-pointer transition-colors duration-200;
    @apply hover:bg-toolbar-hover;
  }

  .layer-item.active {
    @apply bg-editor-accent text-white;
  }

  /* 滑块样式 */
  .slider {
    @apply w-full h-2 bg-toolbar-hover rounded-lg appearance-none cursor-pointer;
  }

  .slider::-webkit-slider-thumb {
    @apply appearance-none w-4 h-4 bg-editor-accent rounded-full cursor-pointer;
  }

  .slider::-moz-range-thumb {
    @apply w-4 h-4 bg-editor-accent rounded-full cursor-pointer border-0;
  }
}

/* 工具类 */
@layer utilities {
  /* 动画类 */
  .animate-fade-in {
    animation: fadeIn 0.2s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-spin-slow {
    animation: spin 3s linear infinite;
  }

  /* 布局类 */
  .editor-layout {
    @apply h-screen flex flex-col;
  }

  .editor-header {
    @apply h-16 flex items-center px-4 bg-toolbar-bg border-b border-toolbar-border;
  }

  .editor-main {
    @apply flex-1 flex;
  }

  .editor-sidebar {
    @apply w-64 bg-editor-panel border-r border-editor-border;
  }

  .editor-canvas {
    @apply flex-1 relative overflow-hidden bg-gray-100;
  }

  /* 状态类 */
  .loading {
    @apply opacity-50 pointer-events-none;
  }

  .disabled {
    @apply opacity-50 cursor-not-allowed;
  }

  .error {
    @apply border-red-500 text-red-500;
  }

  .success {
    @apply border-green-500 text-green-500;
  }

  /* 隐藏类 */
  .sr-only {
    @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0;
    clip: rect(0, 0, 0, 0);
  }
}

/* 动画定义 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Canvas 相关样式 */
.canvas-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.canvas-container canvas {
  display: block;
  max-width: 100%;
  max-height: 100%;
  margin: auto;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 拖拽样式 */
.drag-over {
  @apply border-2 border-dashed border-editor-accent bg-editor-accent bg-opacity-10;
}

/* 工具提示样式 */
.tooltip {
  @apply absolute z-tooltip px-2 py-1 text-xs bg-gray-900 text-white rounded;
  @apply opacity-0 pointer-events-none transition-opacity duration-200;
}

.tooltip.show {
  @apply opacity-100;
}

/* 模态框样式 */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 z-modal;
}

.modal-content {
  @apply bg-white rounded-lg shadow-xl max-w-md w-full mx-4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .editor-sidebar {
    @apply w-full h-64 border-r-0 border-t border-editor-border;
  }

  .editor-main {
    @apply flex-col;
  }

  .toolbar-item {
    @apply p-3;
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  :root {
    color-scheme: dark;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }

  .editor-canvas {
    @apply shadow-none;
  }
}
