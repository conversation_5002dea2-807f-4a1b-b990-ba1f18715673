// PhotoEditor 2.0 - 工具类单元测试

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { BaseTool } from '../../src/lib/tools/BaseTool';
import { BrushTool } from '../../src/lib/tools/BrushTool';
import { SelectionTool } from '../../src/lib/tools/SelectionTool';
import { CropTool } from '../../src/lib/tools/CropTool';
import { MosaicTool } from '../../src/lib/tools/MosaicTool';
import { ToolType } from '../../src/lib/types';

// 创建模拟对象
const mockCanvas = {
  isDrawingMode: false,
  selection: true,
  forEachObject: vi.fn(),
  renderAll: vi.fn(),
  getPointer: vi.fn(() => ({ x: 0, y: 0 }))
} as any;

const mockEventManager = {
  emit: vi.fn()
} as any;

describe('工具类继承测试', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('BrushTool 应该正确继承 BaseTool', () => {
    const brushTool = new BrushTool(mockCanvas, mockEventManager);
    
    // 验证类型
    expect(brushTool.type).toBe(ToolType.BRUSH);
    expect(brushTool.name).toBe('画笔工具');
    
    // 验证方法继承
    expect(typeof brushTool.activate).toBe('function');
    expect(typeof brushTool.deactivate).toBe('function');
    
    // 测试激活方法
    brushTool.activate();
    expect(mockCanvas.isDrawingMode).toBe(true);
    expect(mockEventManager.emit).toHaveBeenCalledWith('tool:activated', { tool: ToolType.BRUSH });
  });

  it('SelectionTool 应该正确继承 BaseTool', () => {
    const selectionTool = new SelectionTool(mockCanvas, mockEventManager);
    
    // 验证类型
    expect(selectionTool.type).toBe(ToolType.SELECTION);
    
    // 测试激活方法
    selectionTool.activate();
    expect(mockCanvas.selection).toBe(true);
    expect(mockEventManager.emit).toHaveBeenCalledWith('tool:activated', { tool: ToolType.SELECTION });
  });

  it('CropTool 应该正确继承 BaseTool', () => {
    const cropTool = new CropTool(mockCanvas, mockEventManager);
    
    // 验证类型
    expect(cropTool.type).toBe(ToolType.CROP);
    
    // 测试激活方法
    cropTool.activate();
    expect(mockEventManager.emit).toHaveBeenCalledWith('tool:activated', { tool: ToolType.CROP });
  });

  it('MosaicTool 应该正确继承 BaseTool', () => {
    const mosaicTool = new MosaicTool(mockCanvas, mockEventManager);
    
    // 验证类型
    expect(mosaicTool.type).toBe(ToolType.MOSAIC);
    
    // 测试激活方法
    mosaicTool.activate();
    expect(mockEventManager.emit).toHaveBeenCalledWith('tool:activated', { tool: ToolType.MOSAIC });
  });
});
