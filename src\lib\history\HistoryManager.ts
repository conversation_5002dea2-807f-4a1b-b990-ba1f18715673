// PhotoEditor 2.0 - 历史管理器
// 负责撤销/重做功能的实现

import type { HistoryEntry, HistoryState } from '$lib/types';

/**
 * 历史管理器
 * 实现撤销/重做功能，使用命令模式管理编辑历史
 */
export class HistoryManager {
  private state: HistoryState;

  constructor(maxSteps: number = 50) {
    this.state = {
      past: [],
      present: null,
      future: [],
      canUndo: false,
      canRedo: false,
      maxSteps
    };
  }

  /**
   * 添加历史记录
   */
  addEntry(entry: HistoryEntry): void {
    // 如果当前有状态，将其移到过去
    if (this.state.present) {
      this.state.past.push(this.state.present);
    }

    // 设置新的当前状态
    this.state.present = entry;

    // 清空未来状态（因为有了新的操作）
    this.state.future = [];

    // 限制历史记录数量
    if (this.state.past.length > this.state.maxSteps) {
      this.state.past.shift();
    }

    this.updateState();
  }

  /**
   * 撤销操作
   */
  undo(): HistoryEntry | null {
    if (!this.state.canUndo || !this.state.present) {
      return null;
    }

    // 将当前状态移到未来
    this.state.future.unshift(this.state.present);

    // 从过去取出最新的状态作为当前状态
    this.state.present = this.state.past.pop() || null;

    this.updateState();
    return this.state.present;
  }

  /**
   * 重做操作
   */
  redo(): HistoryEntry | null {
    if (!this.state.canRedo) {
      return null;
    }

    // 将当前状态移到过去
    if (this.state.present) {
      this.state.past.push(this.state.present);
    }

    // 从未来取出最新的状态作为当前状态
    this.state.present = this.state.future.shift() || null;

    this.updateState();
    return this.state.present;
  }

  /**
   * 获取当前历史状态
   */
  getState(): HistoryState {
    return { ...this.state };
  }

  /**
   * 获取历史记录列表
   */
  getHistory(): HistoryEntry[] {
    const history: HistoryEntry[] = [];

    // 添加过去的记录
    history.push(...this.state.past);

    // 添加当前记录
    if (this.state.present) {
      history.push(this.state.present);
    }

    return history;
  }

  /**
   * 获取当前状态
   */
  getCurrentEntry(): HistoryEntry | null {
    return this.state.present;
  }

  /**
   * 清空历史记录
   */
  clear(): void {
    this.state.past = [];
    this.state.present = null;
    this.state.future = [];
    this.updateState();
  }

  /**
   * 设置最大历史步数
   */
  setMaxSteps(maxSteps: number): void {
    this.state.maxSteps = maxSteps;

    // 如果当前历史记录超过限制，删除最旧的记录
    while (this.state.past.length > maxSteps) {
      this.state.past.shift();
    }

    this.updateState();
  }

  /**
   * 获取历史记录统计信息
   */
  getStats(): {
    totalEntries: number;
    pastEntries: number;
    futureEntries: number;
    memoryUsage: number;
  } {
    const totalEntries =
      this.state.past.length + (this.state.present ? 1 : 0) + this.state.future.length;

    // 估算内存使用量（简单计算）
    let memoryUsage = 0;
    const allEntries = [
      ...this.state.past,
      ...(this.state.present ? [this.state.present] : []),
      ...this.state.future
    ];

    allEntries.forEach(entry => {
      if (entry.data) {
        // 简单估算：JSON 字符串长度 * 2（Unicode 字符）
        memoryUsage += JSON.stringify(entry.data).length * 2;
      }
      if (entry.preview) {
        // 预览图片大小估算
        memoryUsage += entry.preview.length;
      }
    });

    return {
      totalEntries,
      pastEntries: this.state.past.length,
      futureEntries: this.state.future.length,
      memoryUsage
    };
  }

  /**
   * 压缩历史记录（删除预览图片以节省内存）
   */
  compressHistory(): void {
    const compressEntry = (entry: HistoryEntry) => {
      if (entry.preview) {
        delete entry.preview;
      }
    };

    // 只保留最近几个记录的预览
    const keepPreviewCount = 5;

    this.state.past.forEach((entry, index) => {
      if (index < this.state.past.length - keepPreviewCount) {
        compressEntry(entry);
      }
    });

    this.state.future.forEach((entry, index) => {
      if (index >= keepPreviewCount) {
        compressEntry(entry);
      }
    });
  }

  /**
   * 创建历史记录快照
   */
  createSnapshot(name: string, data: any, generatePreview: boolean = false): HistoryEntry {
    const entry: HistoryEntry = {
      id: this.generateId(),
      name,
      timestamp: Date.now(),
      data: this.deepClone(data)
    };

    if (generatePreview && typeof data === 'object' && data.toDataURL) {
      try {
        entry.preview = data.toDataURL('image/jpeg', 0.3); // 低质量预览
      } catch (error) {
        console.warn('Failed to generate preview:', error);
      }
    }

    return entry;
  }

  /**
   * 更新内部状态
   */
  private updateState(): void {
    this.state.canUndo = this.state.past.length > 0;
    this.state.canRedo = this.state.future.length > 0;
  }

  /**
   * 深度克隆对象
   */
  private deepClone(obj: any): any {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }

    if (obj instanceof Date) {
      return new Date(obj.getTime());
    }

    if (obj instanceof Array) {
      return obj.map(item => this.deepClone(item));
    }

    if (typeof obj === 'object') {
      const cloned: any = {};
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          cloned[key] = this.deepClone(obj[key]);
        }
      }
      return cloned;
    }

    return obj;
  }

  /**
   * 生成唯一 ID
   */
  private generateId(): string {
    return `history_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 销毁历史管理器
   */
  destroy(): void {
    this.clear();
  }
}
