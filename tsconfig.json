{
  "extends": "./.svelte-kit/tsconfig.json",
  "compilerOptions": {
    // 严格模式配置
    "strict": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "allowUnusedLabels": false,
    "allowUnreachableCode": false,

    // 模块解析
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,

    // 输出配置
    "noEmit": true,
    "skipLibCheck": true,

    // 类型检查配置
    "forceConsistentCasingInFileNames": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true
  },
  "include": [
    "src/ambient.d.ts",
    "./src/**/*.d.ts",
    "./src/**/*.ts",
    "./src/**/*.js",
    "./src/**/*.svelte",
    "./tests/**/*.ts",
    "./tests/**/*.js"
  ],
  "exclude": ["node_modules", ".svelte-kit", "build", "dist"]
}
