// PhotoEditor 2.0 - 滤镜引擎
// 负责图像滤镜和效果处理

import { FilterType, type Filter } from '$lib/types';
import { fabric } from 'fabric';

/**
 * 滤镜引擎
 * 提供各种图像滤镜和效果处理功能
 */
export class FilterEngine {
  private canvas: fabric.Canvas;
  private webglSupported: boolean;

  constructor(canvas: fabric.Canvas) {
    this.canvas = canvas;
    this.webglSupported = this.checkWebGLSupport();
  }

  /**
   * 检查 WebGL 支持
   */
  private checkWebGLSupport(): boolean {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      return !!gl;
    } catch (e) {
      return false;
    }
  }

  /**
   * 应用滤镜
   */
  async applyFilter(filter: Filter): Promise<void> {
    if (!filter.enabled) return;

    const activeObject = this.canvas.getActiveObject();
    if (!activeObject || !(activeObject instanceof fabric.Image)) {
      throw new Error('No image object selected');
    }

    const fabricFilter = this.createFabricFilter(filter);
    if (fabricFilter) {
      activeObject.filters = activeObject.filters || [];
      activeObject.filters.push(fabricFilter);
      activeObject.applyFilters();
    } else {
      // 使用自定义滤镜处理
      await this.applyCustomFilter(activeObject, filter);
    }
  }

  /**
   * 创建 Fabric.js 滤镜
   */
  private createFabricFilter(filter: Filter): fabric.IBaseFilter | null {
    switch (filter.type) {
      case FilterType.BLUR:
        return new fabric.Image.filters.Blur({
          blur: (filter.params.blur as number) || 0.1
        });

      case FilterType.BRIGHTNESS:
        return new fabric.Image.filters.Brightness({
          brightness: (filter.params.brightness as number) || 0
        });

      case FilterType.CONTRAST:
        return new fabric.Image.filters.Contrast({
          contrast: (filter.params.contrast as number) || 0
        });

      case FilterType.SATURATION:
        return new fabric.Image.filters.Saturation({
          saturation: (filter.params.saturation as number) || 0
        });

      case FilterType.HUE:
        return new fabric.Image.filters.HueRotation({
          rotation: (filter.params.rotation as number) || 0
        });

      case FilterType.NOISE:
        return new fabric.Image.filters.Noise({
          noise: (filter.params.noise as number) || 0
        });

      default:
        return null;
    }
  }

  /**
   * 应用自定义滤镜
   */
  private async applyCustomFilter(imageObject: fabric.Image, filter: Filter): Promise<void> {
    const imageElement = imageObject.getElement() as HTMLImageElement;
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d')!;

    canvas.width = imageElement.width;
    canvas.height = imageElement.height;
    ctx.drawImage(imageElement, 0, 0);

    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const processedImageData = await this.processImageData(imageData, filter);

    ctx.putImageData(processedImageData, 0, 0);

    // 更新图像对象
    imageObject.setSrc(canvas.toDataURL(), () => {
      this.canvas.renderAll();
    });
  }

  /**
   * 处理图像数据
   */
  private async processImageData(imageData: ImageData, filter: Filter): Promise<ImageData> {
    const data = imageData.data;
    const width = imageData.width;
    const height = imageData.height;

    switch (filter.type) {
      case FilterType.GAUSSIAN_BLUR:
        return this.applyGaussianBlur(imageData, (filter.params.radius as number) || 1);

      case FilterType.SHARPEN:
        return this.applySharpen(imageData, (filter.params.amount as number) || 1);

      case FilterType.EMBOSS:
        return this.applyEmboss(imageData);

      case FilterType.EDGE_DETECT:
        return this.applyEdgeDetection(imageData);

      default:
        return imageData;
    }
  }

  /**
   * 应用高斯模糊
   */
  private applyGaussianBlur(imageData: ImageData, radius: number): ImageData {
    const data = imageData.data;
    const width = imageData.width;
    const height = imageData.height;
    const output = new Uint8ClampedArray(data);

    // 简化的高斯模糊实现
    const kernel = this.generateGaussianKernel(radius);
    const kernelSize = kernel.length;
    const half = Math.floor(kernelSize / 2);

    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        let r = 0,
          g = 0,
          b = 0,
          a = 0;
        let weightSum = 0;

        for (let ky = 0; ky < kernelSize; ky++) {
          for (let kx = 0; kx < kernelSize; kx++) {
            const px = x + kx - half;
            const py = y + ky - half;

            if (px >= 0 && px < width && py >= 0 && py < height) {
              const weight = kernel[ky][kx];
              const idx = (py * width + px) * 4;

              r += data[idx] * weight;
              g += data[idx + 1] * weight;
              b += data[idx + 2] * weight;
              a += data[idx + 3] * weight;
              weightSum += weight;
            }
          }
        }

        const idx = (y * width + x) * 4;
        output[idx] = r / weightSum;
        output[idx + 1] = g / weightSum;
        output[idx + 2] = b / weightSum;
        output[idx + 3] = a / weightSum;
      }
    }

    return new ImageData(output, width, height);
  }

  /**
   * 生成高斯核
   */
  private generateGaussianKernel(radius: number): number[][] {
    const size = Math.ceil(radius) * 2 + 1;
    const kernel: number[][] = [];
    const sigma = radius / 3;
    const twoSigmaSquare = 2 * sigma * sigma;
    const center = Math.floor(size / 2);
    let sum = 0;

    for (let y = 0; y < size; y++) {
      kernel[y] = [];
      for (let x = 0; x < size; x++) {
        const dx = x - center;
        const dy = y - center;
        const distance = dx * dx + dy * dy;
        const value = Math.exp(-distance / twoSigmaSquare);
        kernel[y][x] = value;
        sum += value;
      }
    }

    // 归一化
    for (let y = 0; y < size; y++) {
      for (let x = 0; x < size; x++) {
        kernel[y][x] /= sum;
      }
    }

    return kernel;
  }

  /**
   * 应用锐化滤镜
   */
  private applySharpen(imageData: ImageData, amount: number): ImageData {
    const kernel = [
      [0, -amount, 0],
      [-amount, 1 + 4 * amount, -amount],
      [0, -amount, 0]
    ];

    return this.applyConvolution(imageData, kernel);
  }

  /**
   * 应用浮雕效果
   */
  private applyEmboss(imageData: ImageData): ImageData {
    const kernel = [
      [-2, -1, 0],
      [-1, 1, 1],
      [0, 1, 2]
    ];

    return this.applyConvolution(imageData, kernel);
  }

  /**
   * 应用边缘检测
   */
  private applyEdgeDetection(imageData: ImageData): ImageData {
    const kernel = [
      [-1, -1, -1],
      [-1, 8, -1],
      [-1, -1, -1]
    ];

    return this.applyConvolution(imageData, kernel);
  }

  /**
   * 应用卷积核
   */
  private applyConvolution(imageData: ImageData, kernel: number[][]): ImageData {
    const data = imageData.data;
    const width = imageData.width;
    const height = imageData.height;
    const output = new Uint8ClampedArray(data);
    const kernelSize = kernel.length;
    const half = Math.floor(kernelSize / 2);

    for (let y = half; y < height - half; y++) {
      for (let x = half; x < width - half; x++) {
        let r = 0,
          g = 0,
          b = 0;

        for (let ky = 0; ky < kernelSize; ky++) {
          for (let kx = 0; kx < kernelSize; kx++) {
            const px = x + kx - half;
            const py = y + ky - half;
            const idx = (py * width + px) * 4;
            const weight = kernel[ky][kx];

            r += data[idx] * weight;
            g += data[idx + 1] * weight;
            b += data[idx + 2] * weight;
          }
        }

        const idx = (y * width + x) * 4;
        output[idx] = Math.max(0, Math.min(255, r));
        output[idx + 1] = Math.max(0, Math.min(255, g));
        output[idx + 2] = Math.max(0, Math.min(255, b));
        // Alpha 通道保持不变
        output[idx + 3] = data[idx + 3];
      }
    }

    return new ImageData(output, width, height);
  }

  /**
   * 移除滤镜
   */
  removeFilter(filterIndex: number): void {
    const activeObject = this.canvas.getActiveObject();
    if (activeObject && activeObject instanceof fabric.Image && activeObject.filters) {
      activeObject.filters.splice(filterIndex, 1);
      activeObject.applyFilters();
      this.canvas.renderAll();
    }
  }

  /**
   * 清除所有滤镜
   */
  clearFilters(): void {
    const activeObject = this.canvas.getActiveObject();
    if (activeObject && activeObject instanceof fabric.Image) {
      activeObject.filters = [];
      activeObject.applyFilters();
      this.canvas.renderAll();
    }
  }

  /**
   * 获取支持的滤镜列表
   */
  getSupportedFilters(): FilterType[] {
    return [
      FilterType.BLUR,
      FilterType.GAUSSIAN_BLUR,
      FilterType.BRIGHTNESS,
      FilterType.CONTRAST,
      FilterType.SATURATION,
      FilterType.HUE,
      FilterType.SHARPEN,
      FilterType.NOISE,
      FilterType.EMBOSS,
      FilterType.EDGE_DETECT
    ];
  }

  /**
   * 检查滤镜是否支持
   */
  isFilterSupported(filterType: FilterType): boolean {
    return this.getSupportedFilters().includes(filterType);
  }
}
