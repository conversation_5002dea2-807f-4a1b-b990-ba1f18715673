<!doctype html>
<html lang="zh-CN" class="%sveltekit.theme%">

<head>
  <meta charset="utf-8" />
  <link rel="icon" href="%sveltekit.assets%/favicon.svg" type="image/svg+xml" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <meta name="description" content="PhotoEditor 2.0 - 专业的在线图片编辑器" />
  <meta name="keywords" content="图片编辑,在线编辑器,图像处理,打码工具" />
  <meta name="author" content="PhotoEditor 2.0 Team" />

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website" />
  <meta property="og:title" content="PhotoEditor 2.0 - 专业在线图片编辑器" />
  <meta property="og:description" content="功能强大的在线图片编辑器，支持图像打码、滤镜效果、AI功能等" />
  <meta property="og:image" content="%sveltekit.assets%/og-image.png" />

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image" />
  <meta property="twitter:title" content="PhotoEditor 2.0 - 专业在线图片编辑器" />
  <meta property="twitter:description" content="功能强大的在线图片编辑器，支持图像打码、滤镜效果、AI功能等" />
  <meta property="twitter:image" content="%sveltekit.assets%/twitter-image.png" />

  <!-- Preload critical resources -->
  <!-- <link
      rel="preload"
      href="%sveltekit.assets%/fonts/inter.woff2"
      as="font"
      type="font/woff2"
      crossorigin
    /> -->

  %sveltekit.head%
</head>

<body data-sveltekit-preload-data="hover" class="bg-editor-bg text-editor-text">
  <div style="display: contents">%sveltekit.body%</div>

  <!-- Global loading indicator -->
  <div id="global-loading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-modal hidden">
    <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
      <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
      <span class="text-gray-700">加载中...</span>
    </div>
  </div>
</body>

</html>