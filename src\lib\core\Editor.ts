// PhotoEditor 2.0 - 编辑器核心类
// 基于项目技术规格文档的编辑器架构

import {
  ImageFormat,
  ToolType,
  type EditorOptions,
  type EditorState,
  type ExportOptions,
  type Filter,
  type Layer,
  type Point,
  type ProjectData
} from '$lib/types';
import { fabric } from 'fabric';

import { EventManager } from '$lib/core/EventManager';
import { FilterEngine } from '$lib/filters/FilterEngine';
import { HistoryManager } from '$lib/history/HistoryManager';
import { LayerManager } from '$lib/layers/LayerManager';
import { ToolManager } from '$lib/tools/ToolManager';

/**
 * PhotoEditor 核心编辑器类
 * 负责协调各个子系统，提供统一的编辑器API
 */
export class PhotoEditor {
  private canvas!: fabric.Canvas;
  private container: HTMLElement;
  private options: EditorOptions;

  // 子系统管理器
  private toolManager!: ToolManager;
  private layerManager!: LayerManager;
  private historyManager!: HistoryManager;
  private filterEngine!: FilterEngine;
  private eventManager!: EventManager;

  // 编辑器状态
  private state!: EditorState;
  private isInitialized = false;

  constructor(container: HTMLElement, options: Partial<EditorOptions> = {}) {
    this.container = container;
    this.options = {
      width: 800,
      height: 600,
      backgroundColor: '#ffffff',
      enableTouch: true,
      enableKeyboard: true,
      maxHistorySteps: 50,
      ...options,
      container
    };

    this.initializeState();
    this.initializeCanvas();
    this.initializeManagers();
    this.bindEvents();

    this.isInitialized = true;
  }

  /**
   * 初始化编辑器状态
   */
  private initializeState(): void {
    this.state = {
      canvas: null,
      activeTool: ToolType.SELECTION,

      zoom: 1,
      pan: { x: 0, y: 0 },
      isLoading: false,
      error: null,
      isDirty: false
    };
  }

  /**
   * 初始化 Canvas
   */
  private initializeCanvas(): void {
    // 创建 canvas 元素
    const canvasElement = document.createElement('canvas');
    canvasElement.id = 'photoeditor-canvas';
    this.container.appendChild(canvasElement);

    // 初始化 Fabric.js Canvas
    this.canvas = new fabric.Canvas(canvasElement, {
      width: this.options.width,
      height: this.options.height,
      backgroundColor: this.options.backgroundColor,
      selection: true,
      preserveObjectStacking: true,
      enableRetinaScaling: true,
      imageSmoothingEnabled: true
    });

    this.state.canvas = this.canvas;
  }

  /**
   * 初始化各个管理器
   */
  private initializeManagers(): void {
    this.eventManager = new EventManager();
    this.toolManager = new ToolManager(this.canvas, this.eventManager);
    this.layerManager = new LayerManager(this.canvas, this.eventManager);
    this.historyManager = new HistoryManager(this.options.maxHistorySteps!);
    this.filterEngine = new FilterEngine(this.canvas);
  }

  /**
   * 绑定事件
   */
  private bindEvents(): void {
    // Canvas 事件
    this.canvas.on('path:created', this.onPathCreated.bind(this));
    this.canvas.on('object:added', this.onObjectAdded.bind(this));
    this.canvas.on('object:removed', this.onObjectRemoved.bind(this));
    this.canvas.on('object:modified', this.onObjectModified.bind(this));

    // 键盘事件
    if (this.options.enableKeyboard) {
      document.addEventListener('keydown', this.onKeyDown.bind(this));
    }

    // 窗口事件
    window.addEventListener('resize', this.onWindowResize.bind(this));
  }

  // ============================================================================
  // 公共 API - 图像操作
  // ============================================================================

  /**
   * 加载图像
   */
  async loadImage(source: string | File | HTMLImageElement): Promise<void> {
    this.state.isLoading = true;

    try {
      let imageUrl: string;

      if (source instanceof File) {
        imageUrl = URL.createObjectURL(source);
      } else if (source instanceof HTMLImageElement) {
        imageUrl = source.src;
      } else {
        imageUrl = source;
      }

      const img = await this.loadImageFromUrl(imageUrl);

      // 创建 Fabric 图像对象
      const fabricImage = new fabric.Image(img, {
        left: 0,
        top: 0,
        selectable: true,
        evented: true
      });

      // 添加到画布
      this.canvas.add(fabricImage);
      this.canvas.centerObject(fabricImage);
      this.canvas.setActiveObject(fabricImage);
      this.canvas.renderAll();

      // 添加到图层管理器
      this.layerManager.addImageLayer(fabricImage);

      // 记录历史
      this.historyManager.addEntry({
        id: this.generateId(),
        name: 'Load Image',
        timestamp: Date.now(),
        data: this.canvas.toJSON()
      });

      this.state.isDirty = true;
    } catch (error) {
      this.state.error = `Failed to load image: ${error}`;
      throw error;
    } finally {
      this.state.isLoading = false;
    }
  }

  /**
   * 从 URL 加载图像
   */
  private loadImageFromUrl(url: string): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.crossOrigin = 'anonymous';
      img.onload = () => resolve(img);
      img.onerror = reject;
      img.src = url;
    });
  }

  /**
   * 导出图像
   */
  async exportImage(options: Partial<ExportOptions> = {}): Promise<Blob> {
    const exportOptions: ExportOptions = {
      format: ImageFormat.PNG,
      quality: 0.9,
      ...options
    };

    return new Promise<Blob>((resolve, reject) => {
      this.canvas.toBlob(
        (blob: Blob | null) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to export canvas as blob'));
          }
        },
        exportOptions.format,
        exportOptions.quality
      );
    });
  }

  /**
   * 保存项目
   */
  async saveProject(): Promise<ProjectData> {
    const projectData: ProjectData = {
      id: this.generateId(),
      name: 'Untitled Project',
      version: '1.0.0',
      canvas: {
        width: this.canvas.width!,
        height: this.canvas.height!,
        backgroundColor: this.canvas.backgroundColor as any
      },
      layers: this.layerManager.getAllLayers(),
      history: this.historyManager.getHistory(),
      metadata: {
        author: 'PhotoEditor 2.0',
        createdAt: Date.now()
      },
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    return projectData;
  }

  // ============================================================================
  // 公共 API - 工具操作
  // ============================================================================

  /**
   * 选择工具
   */
  selectTool(toolType: ToolType): void {
    this.toolManager.selectTool(toolType);
    this.state.activeTool = toolType;
  }

  /**
   * 获取当前工具
   */
  getActiveTool(): ToolType {
    return this.state.activeTool;
  }

  // ============================================================================
  // 公共 API - 图层操作
  // ============================================================================

  /**
   * 添加图层
   */
  addLayer(type: string, data?: any): Layer {
    return this.layerManager.addLayer(type as any, data);
  }

  /**
   * 删除图层
   */
  deleteLayer(layerId: string): void {
    this.layerManager.removeLayer(layerId);
  }

  /**
   * 获取所有图层
   */
  getLayers(): Layer[] {
    return this.layerManager.getAllLayers();
  }

  // ============================================================================
  // 公共 API - 滤镜操作
  // ============================================================================

  /**
   * 应用滤镜
   */
  async applyFilter(filter: Filter): Promise<void> {
    await this.filterEngine.applyFilter(filter);
    this.canvas.renderAll();
    this.state.isDirty = true;
  }

  // ============================================================================
  // 公共 API - 历史操作
  // ============================================================================

  /**
   * 撤销
   */
  undo(): boolean {
    const entry = this.historyManager.undo();
    if (entry) {
      this.canvas.loadFromJSON(entry.data, () => {
        this.canvas.renderAll();
      });
      return true;
    }
    return false;
  }

  /**
   * 重做
   */
  redo(): boolean {
    const entry = this.historyManager.redo();
    if (entry) {
      this.canvas.loadFromJSON(entry.data, () => {
        this.canvas.renderAll();
      });
      return true;
    }
    return false;
  }

  // ============================================================================
  // 公共 API - 视图操作
  // ============================================================================

  /**
   * 缩放
   */
  setZoom(zoom: number, center?: Point): void {
    const point = center ? new fabric.Point(center.x, center.y) : undefined;
    this.canvas.zoomToPoint(point, zoom);
    this.state.zoom = zoom;
  }

  /**
   * 平移
   */
  pan(deltaX: number, deltaY: number): void {
    const vpt = this.canvas.viewportTransform!;
    vpt[4] += deltaX;
    vpt[5] += deltaY;
    this.canvas.setViewportTransform(vpt);
    this.state.pan = { x: vpt[4], y: vpt[5] };
  }

  // ============================================================================
  // 事件处理
  // ============================================================================

  private onPathCreated(_event: fabric.IEvent): void {
    // 处理路径创建事件
  }

  private onObjectAdded(_event: fabric.IEvent): void {
    this.state.isDirty = true;
  }

  private onObjectRemoved(_event: fabric.IEvent): void {
    this.state.isDirty = true;
  }

  private onObjectModified(_event: fabric.IEvent): void {
    this.state.isDirty = true;
  }

  private onKeyDown(event: KeyboardEvent): void {
    // 处理快捷键
    if (event.ctrlKey || event.metaKey) {
      switch (event.key) {
        case 'z':
          event.preventDefault();
          if (event.shiftKey) {
            this.redo();
          } else {
            this.undo();
          }
          break;
        case 's':
          event.preventDefault();
          this.saveProject();
          break;
      }
    }
  }

  private onWindowResize(): void {
    // 处理窗口大小变化
    const containerRect = this.container.getBoundingClientRect();
    this.canvas.setDimensions({
      width: containerRect.width,
      height: containerRect.height
    });
  }

  // ============================================================================
  // 工具方法
  // ============================================================================

  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 获取编辑器状态
   */
  getState(): EditorState {
    return { ...this.state };
  }

  /**
   * 获取 Canvas 实例
   */
  getCanvas(): fabric.Canvas {
    return this.canvas;
  }

  /**
   * 销毁编辑器
   */
  destroy(): void {
    if (this.canvas) {
      this.canvas.dispose();
    }

    if (this.options.enableKeyboard) {
      document.removeEventListener('keydown', this.onKeyDown.bind(this));
    }

    window.removeEventListener('resize', this.onWindowResize.bind(this));

    this.isInitialized = false;
  }
}
