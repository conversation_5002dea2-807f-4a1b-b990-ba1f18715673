import { fontFamily } from 'tailwindcss/defaultTheme';

/** @type {import('tailwindcss').Config} */
export default {
  content: ['./src/**/*.{html,js,svelte,ts}'],
  darkMode: 'class',
  theme: {
    extend: {
      // 自定义颜色主题
      colors: {
        // 编辑器主题色
        editor: {
          bg: '#1a1a1a',
          panel: '#2a2a2a',
          border: '#3a3a3a',
          text: '#ffffff',
          'text-secondary': '#cccccc',
          accent: '#007acc',
          'accent-hover': '#005a9e'
        },
        // 工具栏颜色
        toolbar: {
          bg: '#333333',
          hover: '#404040',
          active: '#007acc',
          border: '#555555'
        },
        // 状态颜色
        status: {
          success: '#4ade80',
          warning: '#fbbf24',
          error: '#ef4444',
          info: '#3b82f6'
        }
      },

      // 自定义字体
      fontFamily: {
        sans: ['Inter', ...fontFamily.sans],
        mono: ['JetBrains Mono', ...fontFamily.mono]
      },

      // 自定义间距
      spacing: {
        18: '4.5rem',
        88: '22rem',
        128: '32rem'
      },

      // 自定义阴影
      boxShadow: {
        editor: '0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)',
        panel: '0 2px 4px -1px rgba(0, 0, 0, 0.2)',
        toolbar: 'inset 0 1px 0 rgba(255, 255, 255, 0.1)'
      },

      // 自定义边框半径
      borderRadius: {
        editor: '0.375rem'
      },

      // 自定义动画
      animation: {
        'fade-in': 'fadeIn 0.2s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'spin-slow': 'spin 3s linear infinite'
      },

      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' }
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' }
        }
      },

      // 自定义网格
      gridTemplateColumns: {
        editor: '250px 1fr 300px',
        toolbar: 'repeat(auto-fit, minmax(40px, 1fr))'
      },

      // 自定义 Z-index
      zIndex: {
        modal: '1000',
        dropdown: '100',
        tooltip: '200',
        overlay: '50'
      }
    }
  },
  plugins: [
    // 添加自定义工具类
    function ({ addUtilities }) {
      const newUtilities = {
        '.scrollbar-hide': {
          '-ms-overflow-style': 'none',
          'scrollbar-width': 'none',
          '&::-webkit-scrollbar': {
            display: 'none'
          }
        },
        '.scrollbar-thin': {
          'scrollbar-width': 'thin',
          '&::-webkit-scrollbar': {
            width: '6px',
            height: '6px'
          },
          '&::-webkit-scrollbar-track': {
            background: '#2a2a2a'
          },
          '&::-webkit-scrollbar-thumb': {
            background: '#555555',
            'border-radius': '3px'
          },
          '&::-webkit-scrollbar-thumb:hover': {
            background: '#666666'
          }
        }
      };
      addUtilities(newUtilities);
    }
  ]
};
