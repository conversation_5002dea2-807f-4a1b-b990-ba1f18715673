# PhotoEditor

> 基于 Svelte + TypeScript 的专业在线图片编辑器

**作者**: luoleyan
**项目启动时间**: 2025年7月17日

[![CI/CD](https://github.com/luoleyan/PhotoEditor/workflows/CI/CD%20Pipeline/badge.svg)](https://github.com/luoleyan/PhotoEditor/actions)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Version](https://img.shields.io/badge/version-0.1.0-green.svg)](package.json)

## 🌟 项目特色

PhotoEditor 是一款现代化的在线图片编辑器，定位为"轻量级专业工具"：

- 🎨 **专业功能**：相比 Canva 更专业的图像处理能力
- ⚡ **轻量高效**：相比 Photoshop 更轻量的使用体验
- 🔒 **隐私保护**：强大的图像打码功能（项目重点）
- 🤖 **AI 增强**：集成 AI 背景移除、智能修复等功能
- 📱 **跨平台**：支持桌面和移动设备

## 🚀 快速开始

### 环境要求

- **Node.js**: 18.0.0 或更高版本
- **包管理器**: npm, yarn 或 pnpm（推荐 pnpm）
- **浏览器**: Chrome 90+, Firefox 88+, Safari 14+

### 安装依赖

```bash
# 使用 pnpm（推荐）
pnpm install

# 或使用 npm
npm install

# 或使用 yarn
yarn install
```

### 开发环境

```bash
# 启动开发服务器
pnpm dev

# 在浏览器中打开 http://localhost:5173
```

### 构建生产版本

```bash
# 构建应用
pnpm build

# 预览构建结果
pnpm preview
```

## 🛠️ 技术栈

### 核心框架

- **[Svelte](https://svelte.dev/)** - 编译时优化的前端框架
- **[SvelteKit](https://kit.svelte.dev/)** - 全栈应用框架
- **[TypeScript](https://www.typescriptlang.org/)** - 类型安全的 JavaScript

### 图像处理

- **[Fabric.js](http://fabricjs.com/)** - 强大的 Canvas 操作库
- **[Konva.js](https://konvajs.org/)** - 高性能 2D 图形库（备选）
- **WebGL** - 硬件加速的图像处理

### AI 功能

- **[TensorFlow.js](https://www.tensorflow.org/js)** - 浏览器端机器学习
- **[OpenCV.js](https://opencv.org/)** - 计算机视觉库

### 样式和 UI

- **[Tailwind CSS](https://tailwindcss.com/)** - 原子化 CSS 框架
- **[PostCSS](https://postcss.org/)** - CSS 后处理器

### 开发工具

- **[Vite](https://vitejs.dev/)** - 快速构建工具
- **[Vitest](https://vitest.dev/)** - 单元测试框架
- **[Playwright](https://playwright.dev/)** - E2E 测试框架
- **[ESLint](https://eslint.org/)** + **[Prettier](https://prettier.io/)** - 代码质量工具

## 📁 项目结构

```
src/
├── lib/                    # 核心库
│   ├── core/              # 编辑器核心
│   │   ├── Editor.ts      # 主编辑器类
│   │   └── EventManager.ts # 事件管理器
│   ├── tools/             # 工具系统
│   │   ├── ToolManager.ts # 工具管理器
│   │   ├── SelectionTool.ts # 选择工具
│   │   ├── BrushTool.ts   # 画笔工具
│   │   ├── CropTool.ts    # 裁剪工具
│   │   └── MosaicTool.ts  # 打码工具 ⭐
│   ├── layers/            # 图层管理
│   │   └── LayerManager.ts
│   ├── filters/           # 滤镜引擎
│   │   └── FilterEngine.ts
│   ├── ai/               # AI 功能
│   ├── privacy/          # 隐私保护
│   ├── stores/           # 状态管理
│   ├── types/            # 类型定义
│   └── utils/            # 工具函数
├── routes/               # 页面路由
├── components/           # UI 组件
│   ├── Editor/          # 编辑器组件
│   ├── Toolbar/         # 工具栏组件
│   ├── Panels/          # 面板组件
│   └── UI/              # 通用 UI 组件
└── tests/               # 测试文件
    ├── unit/            # 单元测试
    └── e2e/             # E2E 测试
```

## 🎯 核心功能

### 基础编辑功能

- ✅ 图像加载、保存、导出
- ✅ 选择、移动、缩放、旋转
- ✅ 裁剪、调整大小
- ✅ 撤销/重做历史管理

### 绘画工具

- ✅ 画笔工具（支持压感）
- ⏳ 橡皮擦工具
- ⏳ 形状工具（矩形、圆形、线条）
- ⏳ 文本工具

### 图像打码功能 ⭐

- ✅ 画笔打码工具
- ✅ 框选打码工具
- ✅ 多种打码效果（模糊、马赛克、像素化）
- ⏳ 智能人脸检测打码
- ⏳ 批量打码处理

### 滤镜和效果

- ✅ 基础滤镜（模糊、锐化、亮度、对比度）
- ✅ 高级滤镜（高斯模糊、浮雕、边缘检测）
- ⏳ 色彩调整（色相、饱和度、色阶、曲线）
- ⏳ 艺术效果

### AI 功能

- ⏳ AI 背景移除
- ⏳ 智能修复
- ⏳ 人脸检测和美化
- ⏳ 图像超分辨率

### 图层系统

- ✅ 图层管理（添加、删除、排序）
- ✅ 图层属性（可见性、透明度、混合模式）
- ⏳ 图层组和嵌套
- ⏳ 调整图层

## 🧪 测试

### 运行测试

```bash
# 单元测试
pnpm test

# 单元测试（监听模式）
pnpm test:watch

# 测试覆盖率
pnpm test:coverage

# E2E 测试
pnpm test:e2e

# E2E 测试（UI 模式）
pnpm test:e2e:ui
```

### 测试策略

- **单元测试**: 覆盖核心业务逻辑和工具函数
- **集成测试**: 测试组件间的交互
- **E2E 测试**: 验证完整的用户工作流程
- **性能测试**: 监控关键操作的性能指标

## 📊 性能优化

### 已实现的优化

- ✅ 代码分割和懒加载
- ✅ 图像处理 Web Workers
- ✅ WebGL 硬件加速
- ✅ 内存池管理
- ✅ 组件级别的优化

### 性能指标

- 🎯 首屏加载时间 < 2s
- 🎯 大图处理响应时间 < 500ms
- 🎯 工具切换延迟 < 100ms
- 🎯 内存使用 < 512MB（处理 10MB 图片）

## 🚀 部署

### Vercel 部署（推荐）

```bash
# 安装 Vercel CLI
npm i -g vercel

# 部署
vercel --prod
```

### 其他平台

- **Netlify**: 支持静态部署
- **GitHub Pages**: 适用于演示版本
- **Docker**: 容器化部署

## 🤝 贡献指南

### 开发流程

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

### 代码规范

- 使用 TypeScript 严格模式
- 遵循 ESLint 和 Prettier 配置
- 编写单元测试覆盖新功能
- 更新相关文档

### 提交规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
feat: 添加新功能
fix: 修复 bug
docs: 更新文档
style: 代码格式调整
refactor: 代码重构
test: 添加测试
chore: 构建工具或依赖更新
```

## 📄 许可证

本项目采用 [MIT 许可证](LICENSE)。

## 🙏 致谢

- [Fabric.js](http://fabricjs.com/) - 强大的 Canvas 库
- [Svelte](https://svelte.dev/) - 优秀的前端框架
- [TensorFlow.js](https://www.tensorflow.org/js) - 浏览器端 AI 能力
- 所有贡献者和开源社区

## 📞 联系我们

- 项目主页: [https://github.com/your-org/photoeditor-2.0](https://github.com/your-org/photoeditor-2.0)
- 问题反馈: [Issues](https://github.com/your-org/photoeditor-2.0/issues)
- 功能建议: [Discussions](https://github.com/your-org/photoeditor-2.0/discussions)

---

**PhotoEditor 2.0** - 让图像编辑更简单、更专业、更安全 🎨
