# 贡献指南

感谢您对 PhotoEditor 项目的关注！我们欢迎所有形式的贡献。

## 🚀 快速开始

### 开发环境要求

- Node.js >= 18.0.0
- npm >= 9.0.0 或 yarn >= 1.22.0
- Git

### 本地开发设置

1. Fork 并克隆仓库
```bash
git clone https://github.com/your-username/PhotoEditor.git
cd PhotoEditor
```

2. 安装依赖
```bash
npm install
```

3. 启动开发服务器
```bash
npm run dev
```

4. 运行测试
```bash
npm run test
npm run test:e2e
```

## 📝 提交规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### 类型说明

- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 示例

```
feat(editor): 添加图像裁剪功能

- 支持自由裁剪和固定比例裁剪
- 添加裁剪预览功能
- 优化裁剪性能

Closes #123
```

## 🔍 代码规范

- 使用 ESLint 和 Prettier 进行代码格式化
- 遵循 TypeScript 严格模式
- 编写单元测试和 E2E 测试
- 添加适当的注释和文档

## 🐛 报告问题

请使用 GitHub Issues 报告问题，包含：

1. 问题描述
2. 复现步骤
3. 期望行为
4. 实际行为
5. 环境信息
6. 截图（如适用）

## 💡 功能建议

欢迎提出新功能建议！请在 Issues 中详细描述：

1. 功能描述
2. 使用场景
3. 预期收益
4. 实现建议

## 📋 Pull Request 流程

1. 创建功能分支
2. 进行开发和测试
3. 确保所有测试通过
4. 更新相关文档
5. 提交 Pull Request
6. 等待代码审查

## 🤝 行为准则

请遵循友善、包容的交流原则，共同维护良好的开源社区环境。

---

**项目维护者**: luoleyan  
**最后更新**: 2025年7月17日
